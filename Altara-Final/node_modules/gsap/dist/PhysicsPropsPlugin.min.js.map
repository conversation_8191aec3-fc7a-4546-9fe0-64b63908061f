{"version": 3, "file": "PhysicsPropsPlugin.min.js", "sources": ["../src/PhysicsPropsPlugin.js"], "sourcesContent": ["/*!\n * PhysicsPropsPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _getUnit, _getStyleSaver, _reverting,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_round = value => Math.round(value * 10000) / 10000,\n\t_bonusValidated = 1, //<name>PhysicsPropsPlugin</name>\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (!_coreInitted) {\n\t\t\t_getUnit = gsap.utils.getUnit;\n\t\t\t_getStyleSaver = gsap.core.getStyleSaver;\n\t\t\t_reverting = gsap.core.reverting || function() {};\n\t\t\t_coreInitted = 1;\n\t\t}\n\t};\n\nclass PhysicsProp {\n\n\tconstructor(target, p, velocity, acceleration, friction, stepsPerTimeUnit) {\n\t\tlet cache = target._gsap,\n\t\t\tcurVal = cache.get(target, p);\n\t\tthis.p = p;\n\t\tthis.set = cache.set(target, p); //setter\n\t\tthis.s = this.val = parseFloat(curVal);\n\t\tthis.u = _getUnit(curVal) || 0;\n\t\tthis.vel = velocity || 0;\n\t\tthis.v = this.vel / stepsPerTimeUnit;\n\t\tif (acceleration || acceleration === 0) {\n\t\t\tthis.acc = acceleration;\n\t\t\tthis.a = this.acc / (stepsPerTimeUnit * stepsPerTimeUnit);\n\t\t} else {\n\t\t\tthis.acc = this.a = 0;\n\t\t}\n\t\tthis.fr = 1 - (friction || 0) ;\n\t}\n\n}\n\n\nexport const PhysicsPropsPlugin = {\n\tversion:\"3.13.0\",\n\tname:\"physicsProps\",\n\tregister: _initCore,\n\tinit(target, value, tween) {\n\t\t_coreInitted || _initCore();\n\t\tlet data = this,\n\t\t\tp;\n\t\tdata.styles = _getStyleSaver && _getStyleSaver(target);\n\t\tdata.target = target;\n\t\tdata.tween = tween;\n\t\tdata.step = 0;\n\t\tdata.sps = 30; //steps per second\n\t\tdata.vProps = [];\n\t\tfor (p in value) {\n\t\t\tlet { velocity, acceleration, friction } = value[p];\n\t\t\tif (velocity || acceleration) {\n\t\t\t\tdata.vProps.push(new PhysicsProp(target, p, velocity, acceleration, friction, data.sps));\n\t\t\t\tdata._props.push(p);\n\t\t\t\t_getStyleSaver && data.styles.save(p);\n\t\t\t\tfriction && (data.hasFr = 1);\n\t\t\t}\n\t\t}\n\t},\n\trender(ratio, data) {\n\t\tlet { vProps, tween, target, step, hasFr, sps } = data,\n\t\t\ti = vProps.length,\n\t\t\ttime = tween._from ? tween._dur - tween._time : tween._time,\n\t\t\tcurProp, steps, remainder, j, tt;\n\t\tif (tween._time || !_reverting()) {\n\t\t\tif (hasFr) {\n\t\t\t\ttime *= sps;\n\t\t\t\tsteps = (time | 0) - step;\n\t\t\t\t/*\n\t\t\t\tNote: rounding errors build up if we walk the calculations backward which we used to do like this to maximize performance:\n\t\t\t\twhile (i--) {\n\t\t\t\t\tcurProp = vProps[i];\n\t\t\t\t\tj = -steps;\n\t\t\t\t\twhile (j--) {\n\t\t\t\t\t\tcurProp.val -= curProp.v;\n\t\t\t\t\t\tcurProp.v /= curProp.fr;\n\t\t\t\t\t\tcurProp.v -= curProp.a;\n\t\t\t\t\t}\n\t\t\t\t\tcurProp.set(target, curProp.p, _round(curProp.val + (curProp.v * remainder * curProp.fr)) + curProp.u);\n\t\t\t\t}\n\t\t\t\tbut now for the sake of accuracy (to ensure rewinding always goes back to EXACTLY the same spot), we force the calculations to go forward every time. So if the tween is going backward, we just start from the beginning and iterate. This is only necessary with friction.\n\t\t\t\t */\n\t\t\t\tif (steps < 0) {\n\t\t\t\t\twhile (i--) {\n\t\t\t\t\t\tcurProp = vProps[i];\n\t\t\t\t\t\tcurProp.v = curProp.vel / sps;\n\t\t\t\t\t\tcurProp.val = curProp.s;\n\t\t\t\t\t}\n\t\t\t\t\ti = vProps.length;\n\t\t\t\t\tdata.step = step = 0;\n\t\t\t\t\tsteps = time | 0;\n\t\t\t\t}\n\t\t\t\tremainder = time % 1;\n\t\t\t\twhile (i--) {\n\t\t\t\t\tcurProp = vProps[i];\n\t\t\t\t\tj = steps;\n\t\t\t\t\twhile (j--) {\n\t\t\t\t\t\tcurProp.v += curProp.a;\n\t\t\t\t\t\tcurProp.v *= curProp.fr;\n\t\t\t\t\t\tcurProp.val += curProp.v;\n\t\t\t\t\t}\n\t\t\t\t\tcurProp.set(target, curProp.p, _round(curProp.val + (curProp.v * remainder * curProp.fr)) + curProp.u);\n\t\t\t\t}\n\t\t\t\tdata.step += steps;\n\n\t\t\t} else {\n\t\t\t\ttt = time * time * 0.5;\n\t\t\t\twhile (i--) {\n\t\t\t\t\tcurProp = vProps[i];\n\t\t\t\t\tcurProp.set(target, curProp.p, _round(curProp.s + curProp.vel * time + curProp.acc * tt) + curProp.u);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tdata.styles.revert();\n\t\t}\n\t},\n\tkill(property) {\n\t\tlet vProps = this.vProps,\n\t\t\ti = vProps.length;\n\t\twhile (i--) {\n\t\t\tvProps[i].p === property && vProps.splice(i, 1);\n\t\t}\n\t}\n};\n\n\n_getGSAP() && gsap.registerPlugin(PhysicsPropsPlugin);\n\nexport { PhysicsPropsPlugin as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_round", "value", "Math", "round", "_initCore", "core", "_coreInitted", "_getUnit", "utils", "getUnit", "_getStyleSaver", "getStyleSaver", "_reverting", "reverting", "PhysicsProp", "target", "p", "velocity", "acceleration", "friction", "stepsPerTimeUnit", "cache", "_gsap", "curVal", "get", "set", "s", "this", "val", "parseFloat", "u", "vel", "v", "acc", "a", "fr", "PhysicsPropsPlugin", "version", "name", "register", "init", "tween", "data", "styles", "step", "sps", "vProps", "push", "_props", "save", "hasFr", "render", "ratio", "curProp", "steps", "remainder", "j", "tt", "i", "length", "time", "_from", "_dur", "_time", "revert", "kill", "property", "splice"], "mappings": ";;;;;;;;;6MAWY,SAAXA,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKE,gBAAkBF,EAClG,SAATG,EAASC,UAASC,KAAKC,MAAc,IAARF,GAAiB,IAElC,SAAZG,EAAYC,GACXR,EAAOQ,GAAQT,IACVU,IACJC,EAAWV,EAAKW,MAAMC,QACtBC,EAAiBb,EAAKQ,KAAKM,cAC3BC,EAAaf,EAAKQ,KAAKQ,WAAa,aACpCP,EAAe,GAMjB,SAFKQ,EAEOC,EAAQC,EAAGC,EAAUC,EAAcC,EAAUC,OACpDC,EAAQN,EAAOO,MAClBC,EAASF,EAAMG,IAAIT,EAAQC,QACvBA,EAAIA,OACJS,IAAMJ,EAAMI,IAAIV,EAAQC,QACxBU,EAAIC,KAAKC,IAAMC,WAAWN,QAC1BO,EAAIvB,EAASgB,IAAW,OACxBQ,IAAMd,GAAY,OAClBe,EAAIL,KAAKI,IAAMX,EAChBF,GAAiC,IAAjBA,QACde,IAAMf,OACNgB,EAAIP,KAAKM,KAAOb,EAAmBA,SAEnCa,IAAMN,KAAKO,EAAI,OAEhBC,GAAK,GAAKhB,GAAY,OA/BzBtB,EAAMS,EAAcC,EAAUG,EAAgBE,EAqCrCwB,EAAqB,CACjCC,QAAQ,SACRC,KAAK,eACLC,SAAUnC,EACVoC,mBAAKzB,EAAQd,EAAOwC,GACnBnC,GAAgBF,QAEfY,EADG0B,EAAOf,SAQNX,KANL0B,EAAKC,OAASjC,GAAkBA,EAAeK,GAC/C2B,EAAK3B,OAASA,EACd2B,EAAKD,MAAQA,EACbC,EAAKE,KAAO,EACZF,EAAKG,IAAM,GACXH,EAAKI,OAAS,GACJ7C,EAAO,OAC2BA,EAAMe,GAA3CC,IAAAA,SAAUC,IAAAA,aAAcC,IAAAA,UAC1BF,GAAYC,KACfwB,EAAKI,OAAOC,KAAK,IAAIjC,EAAYC,EAAQC,EAAGC,EAAUC,EAAcC,EAAUuB,EAAKG,MACnFH,EAAKM,OAAOD,KAAK/B,GACjBN,GAAkBgC,EAAKC,OAAOM,KAAKjC,GACnCG,IAAauB,EAAKQ,MAAQ,MAI7BC,uBAAOC,EAAOV,OAIZW,EAASC,EAAOC,EAAWC,EAAGC,EAHzBX,EAA4CJ,EAA5CI,OAAQL,EAAoCC,EAApCD,MAAO1B,EAA6B2B,EAA7B3B,OAAQ6B,EAAqBF,EAArBE,KAAMM,EAAeR,EAAfQ,MAAOL,EAAQH,EAARG,IACzCa,EAAIZ,EAAOa,OACXC,EAAOnB,EAAMoB,MAAQpB,EAAMqB,KAAOrB,EAAMsB,MAAQtB,EAAMsB,SAEnDtB,EAAMsB,QAAUnD,OACfsC,EAAO,KAEVI,GAAgB,GADhBM,GAAQf,IACaD,GAeT,EAAG,MACPc,MACNL,EAAUP,EAAOY,IACT1B,EAAIqB,EAAQtB,IAAMc,EAC1BQ,EAAQzB,IAAMyB,EAAQ3B,EAEvBgC,EAAIZ,EAAOa,OACXjB,EAAKE,KAAOA,EAAO,EACnBU,EAAe,EAAPM,MAETL,EAAYK,EAAO,EACZF,KAAK,KACXL,EAAUP,EAAOY,GACjBF,EAAIF,EACGE,KACNH,EAAQrB,GAAKqB,EAAQnB,EACrBmB,EAAQrB,GAAKqB,EAAQlB,GACrBkB,EAAQzB,KAAOyB,EAAQrB,EAExBqB,EAAQ5B,IAAIV,EAAQsC,EAAQrC,EAAGhB,EAAOqD,EAAQzB,IAAOyB,EAAQrB,EAAIuB,EAAYF,EAAQlB,IAAOkB,EAAQvB,GAErGY,EAAKE,MAAQU,WAGbG,EAAKG,EAAOA,EAAO,GACZF,MACNL,EAAUP,EAAOY,IACTjC,IAAIV,EAAQsC,EAAQrC,EAAGhB,EAAOqD,EAAQ3B,EAAI2B,EAAQtB,IAAM6B,EAAOP,EAAQpB,IAAMwB,GAAMJ,EAAQvB,QAIrGY,EAAKC,OAAOqB,UAGdC,mBAAKC,WACApB,EAASnB,KAAKmB,OACjBY,EAAIZ,EAAOa,OACLD,KACNZ,EAAOY,GAAG1C,IAAMkD,GAAYpB,EAAOqB,OAAOT,EAAG,KAMhD9D,KAAcC,EAAKE,eAAeqC"}