{"version": 3, "file": "Draggable.min.js", "sources": ["../src/utils/matrix.js", "../src/Draggable.js"], "sourcesContent": ["/*!\n * matrix 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _doc, _win, _doc<PERSON><PERSON>, _body,\t_div<PERSON><PERSON>r, _svg<PERSON><PERSON>r, _identityMatrix, _gEl,\n\t_transformProp = \"transform\",\n\t_transformOriginProp = _transformProp + \"Origin\",\n\t_hasOffsetBug,\n\t_setDoc = element => {\n\t\tlet doc = element.ownerDocument || element;\n\t\tif (!(_transformProp in element.style) && \"msTransform\" in element.style) { //to improve compatibility with old Microsoft browsers\n\t\t\t_transformProp = \"msTransform\";\n\t\t\t_transformOriginProp = _transformProp + \"Origin\";\n\t\t}\n\t\twhile (doc.parentNode && (doc = doc.parentNode)) {\t}\n\t\t_win = window;\n\t\t_identityMatrix = new Matrix2D();\n\t\tif (doc) {\n\t\t\t_doc = doc;\n\t\t\t_docElement = doc.documentElement;\n\t\t\t_body = doc.body;\n\t\t\t_gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n\t\t\t// prevent any existing CSS from transforming it\n\t\t\t_gEl.style.transform = \"none\";\n\t\t\t// now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon.\n\t\t\tlet d1 = doc.createElement(\"div\"),\n\t\t\t\td2 = doc.createElement(\"div\"),\n\t\t\t\troot = doc && (doc.body || doc.firstElementChild);\n\t\t\tif (root && root.appendChild) {\n\t\t\t\troot.appendChild(d1);\n\t\t\t\td1.appendChild(d2);\n\t\t\t\td1.setAttribute(\"style\", \"position:static;transform:translate3d(0,0,1px)\");\n\t\t\t\t_hasOffsetBug = (d2.offsetParent !== d1);\n\t\t\t\troot.removeChild(d1);\n\t\t\t}\n\t\t}\n\t\treturn doc;\n\t},\n\t_forceNonZeroScale = e => { // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n\t\tlet a, cache;\n\t\twhile (e && e !== _body) {\n\t\t\tcache = e._gsap;\n\t\t\tcache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\t\t\tif (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n\t\t\t\tcache.scaleX = cache.scaleY = 1e-4;\n\t\t\t\tcache.renderTransform(1, cache);\n\t\t\t\ta ? a.push(cache) : (a = [cache]);\n\t\t\t}\n\t\t\te = e.parentNode;\n\t\t}\n\t\treturn a;\n\t},\n\t// possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n\t// _forceDisplay = e => {\n\t// \tlet a = [],\n\t// \t\tparent;\n\t// \twhile (e && e !== _body) {\n\t// \t\tparent = e.parentNode;\n\t// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n\t// \t\tparent || _body.appendChild(e);\n\t// \t\te = parent;\n\t// \t}\n\t// \treturn a;\n\t// },\n\t// _revertDisplay = a => {\n\t// \tfor (let i = 0; i < a.length; i+=3) {\n\t// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n\t// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n\t// \t}\n\t// },\n\t_svgTemps = [], //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n\t_divTemps = [],\n\t_getDocScrollTop = () => _win.pageYOffset  || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0,\n\t_getDocScrollLeft = () => _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0,\n\t_svgOwner = element => element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null),\n\t_isFixed = element => {\n\t\tif (_win.getComputedStyle(element).position === \"fixed\") {\n\t\t\treturn true;\n\t\t}\n\t\telement = element.parentNode;\n\t\tif (element && element.nodeType === 1) { // avoid document fragments which will throw an error.\n\t\t\treturn _isFixed(element);\n\t\t}\n\t},\n\t_createSibling = (element, i) => {\n\t\tif (element.parentNode && (_doc || _setDoc(element))) {\n\t\t\tlet svg = _svgOwner(element),\n\t\t\t\tns = svg ? (svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\") : \"http://www.w3.org/1999/xhtml\",\n\t\t\t\ttype = svg ? (i ? \"rect\" : \"g\") : \"div\",\n\t\t\t\tx = i !== 2 ? 0 : 100,\n\t\t\t\ty = i === 3 ? 100 : 0,\n\t\t\t\tcss = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n\t\t\t\te = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n\t\t\tif (i) {\n\t\t\t\tif (!svg) {\n\t\t\t\t\tif (!_divContainer) {\n\t\t\t\t\t\t_divContainer = _createSibling(element);\n\t\t\t\t\t\t_divContainer.style.cssText = css;\n\t\t\t\t\t}\n\t\t\t\t\te.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n\t\t\t\t\t_divContainer.appendChild(e);\n\n\t\t\t\t} else {\n\t\t\t\t\t_svgContainer || (_svgContainer = _createSibling(element));\n\t\t\t\t\te.setAttribute(\"width\", 0.01);\n\t\t\t\t\te.setAttribute(\"height\", 0.01);\n\t\t\t\t\te.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n\t\t\t\t\t_svgContainer.appendChild(e);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn e;\n\t\t}\n\t\tthrow \"Need document and parent.\";\n\t},\n\t_consolidate = m => { // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\tlet c = new Matrix2D(),\n\t\t\ti = 0;\n\t\tfor (; i < m.numberOfItems; i++) {\n\t\t\tc.multiply(m.getItem(i).matrix);\n\t\t}\n\t\treturn c;\n\t},\n\t_getCTM = svg => {\n\t\tlet m = svg.getCTM(),\n\t\t\ttransform;\n\t\tif (!m) { // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n\t\t\ttransform = svg.style[_transformProp];\n\t\t\tsvg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\t\t\tsvg.appendChild(_gEl);\n\t\t\tm = _gEl.getCTM();\n\t\t\tsvg.removeChild(_gEl);\n\t\t\ttransform ? (svg.style[_transformProp] = transform) : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n\t\t}\n\t\treturn m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n\t},\n\t_placeSiblings = (element, adjustGOffset) => {\n\t\tlet svg = _svgOwner(element),\n\t\t\tisRootSVG = element === svg,\n\t\t\tsiblings = svg ? _svgTemps : _divTemps,\n\t\t\tparent = element.parentNode,\n\t\t\tappendToEl = parent && !svg && parent.shadowRoot && parent.shadowRoot.appendChild ? parent.shadowRoot : parent,\n\t\t\tcontainer, m, b, x, y, cs;\n\t\tif (element === _win) {\n\t\t\treturn element;\n\t\t}\n\t\tsiblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n\t\tcontainer = svg ? _svgContainer : _divContainer;\n\t\tif (svg) {\n\t\t\tif (isRootSVG) {\n\t\t\t\tb = _getCTM(element);\n\t\t\t\tx = -b.e / b.a;\n\t\t\t\ty = -b.f / b.d;\n\t\t\t\tm = _identityMatrix;\n\t\t\t} else if (element.getBBox) {\n\t\t\t\tb = element.getBBox();\n\t\t\t\tm = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\t\t\t\tm = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\t\t\tx = m.a * b.x + m.c * b.y;\n\t\t\t\ty = m.b * b.x + m.d * b.y;\n\t\t\t} else { // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n\t\t\t\tm = new Matrix2D();\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\tif (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\t(isRootSVG ? svg : parent).appendChild(container);\n\t\t\tcontainer.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n\t\t} else {\n\t\t\tx = y = 0;\n\t\t\tif (_hasOffsetBug) { // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n\t\t\t\tm = element.offsetParent;\n\t\t\t\tb = element;\n\t\t\t\twhile (b && (b = b.parentNode) && b !== m && b.parentNode) {\n\t\t\t\t\tif ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n\t\t\t\t\t\tx = b.offsetLeft;\n\t\t\t\t\t\ty = b.offsetTop;\n\t\t\t\t\t\tb = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcs = _win.getComputedStyle(element);\n\t\t\tif (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n\t\t\t\tm = element.offsetParent;\n\t\t\t\twhile (parent && parent !== m) { // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n\t\t\t\t\tx += parent.scrollLeft || 0;\n\t\t\t\t\ty += parent.scrollTop || 0;\n\t\t\t\t\tparent = parent.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\tb = container.style;\n\t\t\tb.top = (element.offsetTop - y) + \"px\";\n\t\t\tb.left = (element.offsetLeft - x) + \"px\";\n\t\t\tb[_transformProp] = cs[_transformProp];\n\t\t\tb[_transformOriginProp] = cs[_transformOriginProp];\n\t\t\t// b.border = m.border;\n\t\t\t// b.borderLeftStyle = m.borderLeftStyle;\n\t\t\t// b.borderTopStyle = m.borderTopStyle;\n\t\t\t// b.borderLeftWidth = m.borderLeftWidth;\n\t\t\t// b.borderTopWidth = m.borderTopWidth;\n\t\t\tb.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n\t\t\tappendToEl.appendChild(container);\n\t\t}\n\t\treturn container;\n\t},\n\t_setMatrix = (m, a, b, c, d, e, f) => {\n\t\tm.a = a;\n\t\tm.b = b;\n\t\tm.c = c;\n\t\tm.d = d;\n\t\tm.e = e;\n\t\tm.f = f;\n\t\treturn m;\n\t};\n\nexport class Matrix2D {\n\tconstructor(a=1, b=0, c=0, d=1, e=0, f=0) {\n\t\t_setMatrix(this, a, b, c, d, e, f);\n\t}\n\n\tinverse() {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\tdeterminant = (a * d - b * c) || 1e-10;\n\t\treturn _setMatrix(\n\t\t\tthis,\n\t\t\td / determinant,\n\t\t\t-b / determinant,\n\t\t\t-c / determinant,\n\t\t\ta / determinant,\n\t\t\t(c * f - d * e) / determinant,\n\t\t\t-(a * f - b * e) / determinant\n\t\t);\n\t}\n\n\tmultiply(matrix) {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\ta2 = matrix.a,\n\t\t\tb2 = matrix.c,\n\t\t\tc2 = matrix.b,\n\t\t\td2 = matrix.d,\n\t\t\te2 = matrix.e,\n\t\t\tf2 = matrix.f;\n\t\treturn _setMatrix(this,\n\t\t\ta2 * a + c2 * c,\n\t\t\ta2 * b + c2 * d,\n\t\t\tb2 * a + d2 * c,\n\t\t\tb2 * b + d2 * d,\n\t\t\te + e2 * a + f2 * c,\n\t\t\tf + e2 * b + f2 * d);\n\t}\n\n\tclone() {\n\t\treturn new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n\t}\n\n\tequals(matrix) {\n\t\tlet {a, b, c, d, e, f} = this;\n\t\treturn (a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f);\n\t}\n\n\tapply(point, decoratee={}) {\n\t\tlet {x, y} = point,\n\t\t\t{a, b, c, d, e, f} = this;\n\t\tdecoratee.x = (x * a + y * c + e) || 0;\n\t\tdecoratee.y = (x * b + y * d + f) || 0;\n\t\treturn decoratee;\n\t}\n\n}\n\n// Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) { // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n\tif (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n\t\treturn new Matrix2D();\n\t}\n\tlet zeroScales = _forceNonZeroScale(element),\n\t\tsvg = _svgOwner(element),\n\t\ttemps = svg ? _svgTemps : _divTemps,\n\t\tcontainer = _placeSiblings(element, adjustGOffset),\n\t\tb1 = temps[0].getBoundingClientRect(),\n\t\tb2 = temps[1].getBoundingClientRect(),\n\t\tb3 = temps[2].getBoundingClientRect(),\n\t\tparent = container.parentNode,\n\t\tisFixed = !includeScrollInFixed && _isFixed(element),\n\t\tm = new Matrix2D(\n\t\t\t(b2.left - b1.left) / 100,\n\t\t\t(b2.top - b1.top) / 100,\n\t\t\t(b3.left - b1.left) / 100,\n\t\t\t(b3.top - b1.top) / 100,\n\t\t\tb1.left + (isFixed ? 0 : _getDocScrollLeft()),\n\t\t\tb1.top + (isFixed ? 0 : _getDocScrollTop())\n\t\t);\n\tparent.removeChild(container);\n\tif (zeroScales) {\n\t\tb1 = zeroScales.length;\n\t\twhile (b1--) {\n\t\t\tb2 = zeroScales[b1];\n\t\t\tb2.scaleX = b2.scaleY = 0;\n\t\t\tb2.renderTransform(1, b2);\n\t\t}\n\t}\n\treturn inverse ? m.inverse() : m;\n}\n\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM };\n\n// export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }", "/*!\n * Draggable 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n */\n/* eslint-disable */\n\nimport { getGlobalMatrix, Matrix2D } from \"./utils/matrix.js\";\n\nlet gsap, _win, _doc, _docElement, _body, _tempDiv, _placeholderDiv, _coreInitted, _checkPrefix, _toArray, _supportsPassive, _isTouchDevice, _touchEventLookup, _isMultiTouching, _isAndroid, InertiaPlugin, _defaultCursor, _supportsPointer, _context, _getStyleSaver,\n\t_dragCount = 0,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isFunction = value => typeof(value) === \"function\",\n\t_isObject = value => typeof(value) === \"object\",\n\t_isUndefined = value => typeof(value) === \"undefined\",\n\t_emptyFunc = () => false,\n\t_transformProp = \"transform\",\n\t_transformOriginProp = \"transformOrigin\",\n\t_round = value => Math.round(value * 10000) / 10000,\n\t_isArray = Array.isArray,\n\t_createElement = (type, ns) => {\n\t\tlet e = _doc.createElementNS ? _doc.createElementNS((ns || \"http://www.w3.org/1999/xhtml\").replace(/^https/, \"http\"), type) : _doc.createElement(type); //some servers swap in https for http in the namespace which can break things, making \"style\" inaccessible.\n\t\treturn e.style ? e : _doc.createElement(type); //some environments won't allow access to the element's style when created with a namespace in which case we default to the standard createElement() to work around the issue. Also note that when GSAP is embedded directly inside an SVG file, createElement() won't allow access to the style object in Firefox (see https://gsap.com/forums/topic/20215-problem-using-tweenmax-in-standalone-self-containing-svg-file-err-cannot-set-property-csstext-of-undefined/).\n\t},\n\t_RAD2DEG = 180 / Math.PI,\n\t_bigNum = 1e20,\n\t_identityMatrix = new Matrix2D(),\n\t_getTime = Date.now || (() => new Date().getTime()),\n\t_renderQueue = [],\n\t_lookup = {}, //when a Draggable is created, the target gets a unique _gsDragID property that allows gets associated with the Draggable instance for quick lookups in Draggable.get(). This avoids circular references that could cause gc problems.\n\t_lookupCount = 0,\n\t_clickableTagExp = /^(?:a|input|textarea|button|select)$/i,\n\t_lastDragTime = 0,\n\t_temp1 = {}, // a simple object we reuse and populate (usually x/y properties) to conserve memory and improve performance.\n\t_windowProxy = {}, //memory/performance optimization - we reuse this object during autoScroll to store window-related bounds/offsets.\n\t_copy = (obj, factor) => {\n\t\tlet copy = {}, p;\n\t\tfor (p in obj) {\n\t\t\tcopy[p] = factor ? obj[p] * factor : obj[p];\n\t\t}\n\t\treturn copy;\n\t},\n\t_extend = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\tif (!(p in obj)) {\n\t\t\t\tobj[p] = defaults[p];\n\t\t\t}\n\t\t}\n\t\treturn obj;\n\t},\n\t_setTouchActionForAllDescendants = (elements, value) => {\n\t\tlet i = elements.length,\n\t\t\tchildren;\n\t\twhile (i--) {\n\t\t\tvalue ? (elements[i].style.touchAction = value) : elements[i].style.removeProperty(\"touch-action\");\n\t\t\tchildren = elements[i].children;\n\t\t\tchildren && children.length && _setTouchActionForAllDescendants(children, value);\n\t\t}\n\t},\n\t_renderQueueTick = () => _renderQueue.forEach(func => func()),\n\t_addToRenderQueue = func => {\n\t\t_renderQueue.push(func);\n\t\tif (_renderQueue.length === 1) {\n\t\t\tgsap.ticker.add(_renderQueueTick);\n\t\t}\n\t},\n\t_renderQueueTimeout = () => !_renderQueue.length && gsap.ticker.remove(_renderQueueTick),\n\t_removeFromRenderQueue = func => {\n\t\tlet i = _renderQueue.length;\n\t\twhile (i--) {\n\t\t\tif (_renderQueue[i] === func) {\n\t\t\t\t_renderQueue.splice(i, 1);\n\t\t\t}\n\t\t}\n\t\tgsap.to(_renderQueueTimeout, {overwrite:true, delay:15, duration:0, onComplete:_renderQueueTimeout, data:\"_draggable\"}); //remove the \"tick\" listener only after the render queue is empty for 15 seconds (to improve performance). Adding/removing it constantly for every click/touch wouldn't deliver optimal speed, and we also don't want the ticker to keep calling the render method when things are idle for long periods of time (we want to improve battery life on mobile devices).\n\t},\n\t_setDefaults = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\tif (!(p in obj)) {\n\t\t\t\tobj[p] = defaults[p];\n\t\t\t}\n\t\t}\n\t\treturn obj;\n\t},\n\t_addListener = (element, type, func, capture) => {\n\t\tif (element.addEventListener) {\n\t\t\tlet touchType = _touchEventLookup[type];\n\t\t\tcapture = capture || (_supportsPassive ? {passive: false} : null);\n\t\t\telement.addEventListener(touchType || type, func, capture);\n\t\t\t(touchType && type !== touchType) && element.addEventListener(type, func, capture);//some browsers actually support both, so must we. But pointer events cover all.\n\t\t}\n\t},\n\t_removeListener = (element, type, func, capture) => {\n\t\tif (element.removeEventListener) {\n\t\t\tlet touchType = _touchEventLookup[type];\n\t\t\telement.removeEventListener(touchType || type, func, capture);\n\t\t\t(touchType && type !== touchType) && element.removeEventListener(type, func, capture);\n\t\t}\n\t},\n\t_preventDefault = event => {\n\t\tevent.preventDefault && event.preventDefault();\n\t\tevent.preventManipulation && event.preventManipulation();  //for some Microsoft browsers\n\t},\n\t_hasTouchID = (list, ID) => {\n\t\tlet i = list.length;\n\t\twhile (i--) {\n\t\t\tif (list[i].identifier === ID) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t},\n\t_onMultiTouchDocumentEnd = event => {\n\t\t_isMultiTouching = (event.touches && _dragCount < event.touches.length);\n\t\t_removeListener(event.target, \"touchend\", _onMultiTouchDocumentEnd);\n\t},\n\n\t_onMultiTouchDocument = event => {\n\t\t_isMultiTouching = (event.touches && _dragCount < event.touches.length);\n\t\t_addListener(event.target, \"touchend\", _onMultiTouchDocumentEnd);\n\t},\n\t_getDocScrollTop = doc => _win.pageYOffset  || doc.scrollTop || doc.documentElement.scrollTop || doc.body.scrollTop || 0,\n\t_getDocScrollLeft = doc => _win.pageXOffset || doc.scrollLeft || doc.documentElement.scrollLeft || doc.body.scrollLeft || 0,\n\t_addScrollListener = (e, callback) => {\n\t\t_addListener(e, \"scroll\", callback);\n\t\tif (!_isRoot(e.parentNode)) {\n\t\t\t_addScrollListener(e.parentNode, callback);\n\t\t}\n\t},\n\t_removeScrollListener = (e, callback) => {\n\t\t_removeListener(e, \"scroll\", callback);\n\t\tif (!_isRoot(e.parentNode)) {\n\t\t\t_removeScrollListener(e.parentNode, callback);\n\t\t}\n\t},\n\t_isRoot = e => !!(!e || e === _docElement || e.nodeType === 9 || e === _doc.body || e === _win || !e.nodeType || !e.parentNode),\n\t_getMaxScroll = (element, axis) => {\n\t\tlet dim = (axis === \"x\") ? \"Width\" : \"Height\",\n\t\t\tscroll = \"scroll\" + dim,\n\t\t\tclient = \"client\" + dim;\n\t\treturn Math.max(0, _isRoot(element) ? Math.max(_docElement[scroll], _body[scroll]) - (_win[\"inner\" + dim] || _docElement[client] || _body[client]) : element[scroll] - element[client]);\n\t},\n\t_recordMaxScrolls = (e, skipCurrent) => { //records _gsMaxScrollX and _gsMaxScrollY properties for the element and all ancestors up the chain so that we can cap it, otherwise dragging beyond the edges with autoScroll on can endlessly scroll.\n\t\tlet x = _getMaxScroll(e, \"x\"),\n\t\t\ty = _getMaxScroll(e, \"y\");\n\t\tif (_isRoot(e)) {\n\t\t\te = _windowProxy;\n\t\t} else {\n\t\t\t_recordMaxScrolls(e.parentNode, skipCurrent);\n\t\t}\n\t\te._gsMaxScrollX = x;\n\t\te._gsMaxScrollY = y;\n\t\tif (!skipCurrent) {\n\t\t\te._gsScrollX = e.scrollLeft || 0;\n\t\t\te._gsScrollY = e.scrollTop || 0;\n\t\t}\n\t},\n\t_setStyle = (element, property, value) => {\n\t\tlet style = element.style;\n\t\tif (!style) {\n\t\t\treturn;\n\t\t}\n\t\tif (_isUndefined(style[property])) {\n\t\t\tproperty = _checkPrefix(property, element) || property;\n\t\t}\n\t\tif (value == null) {\n\t\t\tstyle.removeProperty && style.removeProperty(property.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n\t\t} else {\n\t\t\tstyle[property] = value;\n\t\t}\n\t},\n\t_getComputedStyle = element => _win.getComputedStyle((element instanceof Element) ? element : element.host || (element.parentNode || {}).host || element), //the \"host\" stuff helps to accommodate ShadowDom objects.\n\n\t_tempRect = {}, //reuse to reduce garbage collection tasks\n\t_parseRect = e => { //accepts a DOM element, a mouse event, or a rectangle object and returns the corresponding rectangle with left, right, width, height, top, and bottom properties\n\t\tif (e === _win) {\n\t\t\t_tempRect.left = _tempRect.top = 0;\n\t\t\t_tempRect.width = _tempRect.right = _docElement.clientWidth || e.innerWidth || _body.clientWidth || 0;\n\t\t\t_tempRect.height = _tempRect.bottom = ((e.innerHeight || 0) - 20 < _docElement.clientHeight) ? _docElement.clientHeight : e.innerHeight || _body.clientHeight || 0;\n\t\t\treturn _tempRect;\n\t\t}\n\t\tlet doc = e.ownerDocument || _doc,\n\t\t\tr = !_isUndefined(e.pageX) ? {left: e.pageX - _getDocScrollLeft(doc), top: e.pageY - _getDocScrollTop(doc), right: e.pageX - _getDocScrollLeft(doc) + 1, bottom: e.pageY - _getDocScrollTop(doc) + 1} : (!e.nodeType && !_isUndefined(e.left) && !_isUndefined(e.top)) ? e : _toArray(e)[0].getBoundingClientRect();\n\t\tif (_isUndefined(r.right) && !_isUndefined(r.width)) {\n\t\t\tr.right = r.left + r.width;\n\t\t\tr.bottom = r.top + r.height;\n\t\t} else if (_isUndefined(r.width)) { //some browsers don't include width and height properties. We can't just set them directly on r because some browsers throw errors, so create a new generic object.\n\t\t\tr = {width: r.right - r.left, height: r.bottom - r.top, right: r.right, left: r.left, bottom: r.bottom, top: r.top};\n\t\t}\n\t\treturn r;\n\t},\n\n\t_dispatchEvent = (target, type, callbackName) => {\n\t\tlet vars = target.vars,\n\t\t\tcallback = vars[callbackName],\n\t\t\tlisteners = target._listeners[type],\n\t\t\tresult;\n\t\tif (_isFunction(callback)) {\n\t\t\tresult = callback.apply(vars.callbackScope || target, vars[callbackName + \"Params\"] || [target.pointerEvent]);\n\t\t}\n\t\tif (listeners && target.dispatchEvent(type) === false) {\n\t\t\tresult = false;\n\t\t}\n\t\treturn result;\n\t},\n\t_getBounds = (target, context) => { //accepts any of the following: a DOM element, jQuery object, selector text, or an object defining bounds as {top, left, width, height} or {minX, maxX, minY, maxY}. Returns an object with left, top, width, and height properties.\n\t\tlet e = _toArray(target)[0],\n\t\t\ttop, left, offset;\n\t\tif (!e.nodeType && e !== _win) {\n\t\t\tif (!_isUndefined(target.left)) {\n\t\t\t\toffset = {x:0, y:0}; //_getOffsetTransformOrigin(context); //the bounds should be relative to the origin\n\t\t\t\treturn {left: target.left - offset.x, top: target.top - offset.y, width: target.width, height: target.height};\n\t\t\t}\n\t\t\tleft = target.min || target.minX || target.minRotation || 0;\n\t\t\ttop = target.min || target.minY || 0;\n\t\t\treturn {left:left, top:top, width:(target.max || target.maxX || target.maxRotation || 0) - left, height:(target.max || target.maxY || 0) - top};\n\t\t}\n\t\treturn _getElementBounds(e, context);\n\t},\n\t_point1 = {}, //we reuse to minimize garbage collection tasks.\n\t_getElementBounds = (element, context) => {\n\t\tcontext = _toArray(context)[0];\n\t\tlet isSVG = (element.getBBox && element.ownerSVGElement),\n\t\t\tdoc = element.ownerDocument || _doc,\n\t\t\tleft, right, top, bottom, matrix, p1, p2, p3, p4, bbox, width, height, cs;\n\t\tif (element === _win) {\n\t\t\ttop = _getDocScrollTop(doc);\n\t\t\tleft = _getDocScrollLeft(doc);\n\t\t\tright = left + (doc.documentElement.clientWidth || element.innerWidth || doc.body.clientWidth || 0);\n\t\t\tbottom = top + (((element.innerHeight || 0) - 20 < doc.documentElement.clientHeight) ? doc.documentElement.clientHeight : element.innerHeight || doc.body.clientHeight || 0); //some browsers (like Firefox) ignore absolutely positioned elements, and collapse the height of the documentElement, so it could be 8px, for example, if you have just an absolutely positioned div. In that case, we use the innerHeight to resolve this.\n\t\t} else if (context === _win || _isUndefined(context)) {\n\t\t\treturn element.getBoundingClientRect();\n\t\t} else {\n\t\t\tleft = top =  0;\n\t\t\tif (isSVG) {\n\t\t\t\tbbox = element.getBBox();\n\t\t\t\twidth = bbox.width;\n\t\t\t\theight = bbox.height;\n\t\t\t} else {\n\t\t\t\tif (element.viewBox && (bbox = element.viewBox.baseVal)) {\n\t\t\t\t\tleft = bbox.x || 0;\n\t\t\t\t\ttop = bbox.y || 0;\n\t\t\t\t\twidth = bbox.width;\n\t\t\t\t\theight = bbox.height;\n\t\t\t\t}\n\t\t\t\tif (!width) {\n\t\t\t\t\tcs = _getComputedStyle(element);\n\t\t\t\t\tbbox = cs.boxSizing === \"border-box\";\n\t\t\t\t\twidth = (parseFloat(cs.width) || element.clientWidth || 0) + (bbox ? 0 : parseFloat(cs.borderLeftWidth) + parseFloat(cs.borderRightWidth));\n\t\t\t\t\theight = (parseFloat(cs.height) || element.clientHeight || 0) + (bbox ? 0 : parseFloat(cs.borderTopWidth) + parseFloat(cs.borderBottomWidth));\n\t\t\t\t}\n\t\t\t}\n\t\t\tright = width;\n\t\t\tbottom = height;\n\t\t}\n\t\tif (element === context) {\n\t\t\treturn {left:left, top:top, width: right - left, height: bottom - top};\n\t\t}\n\t\tmatrix = getGlobalMatrix(context, true).multiply(getGlobalMatrix(element));\n\t\tp1 = matrix.apply({x:left, y:top});\n\t\tp2 = matrix.apply({x:right, y:top});\n\t\tp3 = matrix.apply({x:right, y:bottom});\n\t\tp4 = matrix.apply({x:left, y:bottom});\n\t\tleft = Math.min(p1.x, p2.x, p3.x, p4.x);\n\t\ttop = Math.min(p1.y, p2.y, p3.y, p4.y);\n\t\treturn {left: left, top: top, width: Math.max(p1.x, p2.x, p3.x, p4.x) - left, height: Math.max(p1.y, p2.y, p3.y, p4.y) - top};\n\t},\n\t_parseInertia = (draggable, snap, max, min, factor, forceZeroVelocity) => {\n\t\tlet vars = {},\n\t\t\ta, i, l;\n\t\tif (snap) {\n\t\t\tif (factor !== 1 && snap instanceof Array) { //some data must be altered to make sense, like if the user passes in an array of rotational values in degrees, we must convert it to radians. Or for scrollLeft and scrollTop, we invert the values.\n\t\t\t\tvars.end = a = [];\n\t\t\t\tl = snap.length;\n\t\t\t\tif (_isObject(snap[0])) { //if the array is populated with objects, like points ({x:100, y:200}), make copies before multiplying by the factor, otherwise we'll mess up the originals and the user may reuse it elsewhere.\n\t\t\t\t\tfor (i = 0; i < l; i++) {\n\t\t\t\t\t\ta[i] = _copy(snap[i], factor);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tfor (i = 0; i < l; i++) {\n\t\t\t\t\t\ta[i] = snap[i] * factor;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tmax += 1.1; //allow 1.1 pixels of wiggle room when snapping in order to work around some browser inconsistencies in the way bounds are reported which can make them roughly a pixel off. For example, if \"snap:[-$('#menu').width(), 0]\" was defined and #menu had a wrapper that was used as the bounds, some browsers would be one pixel off, making the minimum -752 for example when snap was [-753,0], thus instead of snapping to -753, it would snap to 0 since -753 was below the minimum.\n\t\t\t\tmin -= 1.1;\n\t\t\t} else if (_isFunction(snap)) {\n\t\t\t\tvars.end = value => {\n\t\t\t\t\tlet result = snap.call(draggable, value),\n\t\t\t\t\t\tcopy, p;\n\t\t\t\t\tif (factor !== 1) {\n\t\t\t\t\t\tif (_isObject(result)) {\n\t\t\t\t\t\t\tcopy = {};\n\t\t\t\t\t\t\tfor (p in result) {\n\t\t\t\t\t\t\t\tcopy[p] = result[p] * factor;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tresult = copy;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresult *= factor;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn result; //we need to ensure that we can scope the function call to the Draggable instance itself so that users can access important values like maxX, minX, maxY, minY, x, and y from within that function.\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tvars.end = snap;\n\t\t\t}\n\t\t}\n\t\tif (max || max === 0) {\n\t\t\tvars.max = max;\n\t\t}\n\t\tif (min || min === 0) {\n\t\t\tvars.min = min;\n\t\t}\n\t\tif (forceZeroVelocity) {\n\t\t\tvars.velocity = 0;\n\t\t}\n\t\treturn vars;\n\t},\n\t_isClickable = element => { //sometimes it's convenient to mark an element as clickable by adding a data-clickable=\"true\" attribute (in which case we won't preventDefault() the mouse/touch event). This method checks if the element is an <a>, <input>, or <button> or has the data-clickable or contentEditable attribute set to true (or any of its parent elements).\n\t\tlet data;\n\t\treturn (!element || !element.getAttribute || element === _body) ? false : ((data = element.getAttribute(\"data-clickable\")) === \"true\" || (data !== \"false\" && (_clickableTagExp.test(element.nodeName + \"\") || element.getAttribute(\"contentEditable\") === \"true\"))) ? true : _isClickable(element.parentNode);\n\t},\n\t_setSelectable = (elements, selectable) => {\n\t\tlet i = elements.length,\n\t\t\te;\n\t\twhile (i--) {\n\t\t\te = elements[i];\n\t\t\te.ondragstart = e.onselectstart = selectable ? null : _emptyFunc;\n\t\t\tgsap.set(e, {lazy:true, userSelect: (selectable ? \"text\" : \"none\")});\n\t\t}\n\t},\n\t_isFixed = element => {\n\t\tif (_getComputedStyle(element).position === \"fixed\") {\n\t\t\treturn true;\n\t\t}\n\t\telement = element.parentNode;\n\t\tif (element && element.nodeType === 1) { // avoid document fragments which will throw an error.\n\t\t\treturn _isFixed(element);\n\t\t}\n\t},\n\t_supports3D, _addPaddingBR,\n\n\t//The ScrollProxy class wraps an element's contents into another div (we call it \"content\") that we either add padding when necessary or apply a translate3d() transform in order to overscroll (scroll past the boundaries). This allows us to simply set the scrollTop/scrollLeft (or top/left for easier reverse-axis orientation, which is what we do in Draggable) and it'll do all the work for us. For example, if we tried setting scrollTop to -100 on a normal DOM element, it wouldn't work - it'd look the same as setting it to 0, but if we set scrollTop of a ScrollProxy to -100, it'll give the correct appearance by either setting paddingTop of the wrapper to 100 or applying a 100-pixel translateY.\n\tScrollProxy = function(element, vars) {\n\t\telement = gsap.utils.toArray(element)[0];\n\t\tvars = vars || {};\n\t\tlet content = document.createElement(\"div\"),\n\t\t\tstyle = content.style,\n\t\t\tnode = element.firstChild,\n\t\t\toffsetTop = 0,\n\t\t\toffsetLeft = 0,\n\t\t\tprevTop = element.scrollTop,\n\t\t\tprevLeft = element.scrollLeft,\n\t\t\tscrollWidth = element.scrollWidth,\n\t\t\tscrollHeight = element.scrollHeight,\n\t\t\textraPadRight = 0,\n\t\t\tmaxLeft = 0,\n\t\t\tmaxTop = 0,\n\t\t\telementWidth, elementHeight, contentHeight, nextNode, transformStart, transformEnd;\n\t\tif (_supports3D && vars.force3D !== false) {\n\t\t\ttransformStart = \"translate3d(\";\n\t\t\ttransformEnd = \"px,0px)\";\n\t\t} else if (_transformProp) {\n\t\t\ttransformStart = \"translate(\";\n\t\t\ttransformEnd = \"px)\";\n\t\t}\n\t\tthis.scrollTop = function(value, force) {\n\t\t\tif (!arguments.length) {\n\t\t\t\treturn -this.top();\n\t\t\t}\n\t\t\tthis.top(-value, force);\n\t\t};\n\t\tthis.scrollLeft = function(value, force) {\n\t\t\tif (!arguments.length) {\n\t\t\t\treturn -this.left();\n\t\t\t}\n\t\t\tthis.left(-value, force);\n\t\t};\n\t\tthis.left = function(value, force) {\n\t\t\tif (!arguments.length) {\n\t\t\t\treturn -(element.scrollLeft + offsetLeft);\n\t\t\t}\n\t\t\tlet dif = element.scrollLeft - prevLeft,\n\t\t\t\toldOffset = offsetLeft;\n\t\t\tif ((dif > 2 || dif < -2) && !force) { //if the user interacts with the scrollbar (or something else scrolls it, like the mouse wheel), we should kill any tweens of the ScrollProxy.\n\t\t\t\tprevLeft = element.scrollLeft;\n\t\t\t\tgsap.killTweensOf(this, {left:1, scrollLeft:1});\n\t\t\t\tthis.left(-prevLeft);\n\t\t\t\tif (vars.onKill) {\n\t\t\t\t\tvars.onKill();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvalue = -value; //invert because scrolling works in the opposite direction\n\t\t\tif (value < 0) {\n\t\t\t\toffsetLeft = (value - 0.5) | 0;\n\t\t\t\tvalue = 0;\n\t\t\t} else if (value > maxLeft) {\n\t\t\t\toffsetLeft = (value - maxLeft) | 0;\n\t\t\t\tvalue = maxLeft;\n\t\t\t} else {\n\t\t\t\toffsetLeft = 0;\n\t\t\t}\n\t\t\tif (offsetLeft || oldOffset) {\n\t\t\t\tif (!this._skip) {\n\t\t\t\t\tstyle[_transformProp] = transformStart + -offsetLeft + \"px,\" + -offsetTop + transformEnd;\n\t\t\t\t}\n\t\t\t\tif (offsetLeft + extraPadRight >= 0) {\n\t\t\t\t\tstyle.paddingRight =  offsetLeft + extraPadRight + \"px\";\n\t\t\t\t}\n\t\t\t}\n\t\t\telement.scrollLeft = value | 0;\n\t\t\tprevLeft = element.scrollLeft; //don't merge this with the line above because some browsers adjust the scrollLeft after it's set, so in order to be 100% accurate in tracking it, we need to ask the browser to report it.\n\t\t};\n\t\tthis.top = function(value, force) {\n\t\t\tif (!arguments.length) {\n\t\t\t\treturn -(element.scrollTop + offsetTop);\n\t\t\t}\n\t\t\tlet dif = element.scrollTop - prevTop,\n\t\t\t\toldOffset = offsetTop;\n\t\t\tif ((dif > 2 || dif < -2) && !force) { //if the user interacts with the scrollbar (or something else scrolls it, like the mouse wheel), we should kill any tweens of the ScrollProxy.\n\t\t\t\tprevTop = element.scrollTop;\n\t\t\t\tgsap.killTweensOf(this, {top:1, scrollTop:1});\n\t\t\t\tthis.top(-prevTop);\n\t\t\t\tif (vars.onKill) {\n\t\t\t\t\tvars.onKill();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvalue = -value; //invert because scrolling works in the opposite direction\n\t\t\tif (value < 0) {\n\t\t\t\toffsetTop = (value - 0.5) | 0;\n\t\t\t\tvalue = 0;\n\t\t\t} else if (value > maxTop) {\n\t\t\t\toffsetTop = (value - maxTop) | 0;\n\t\t\t\tvalue = maxTop;\n\t\t\t} else {\n\t\t\t\toffsetTop = 0;\n\t\t\t}\n\t\t\tif (offsetTop || oldOffset) {\n\t\t\t\tif (!this._skip) {\n\t\t\t\t\tstyle[_transformProp] = transformStart + -offsetLeft + \"px,\" + -offsetTop + transformEnd;\n\t\t\t\t}\n\t\t\t}\n\t\t\telement.scrollTop = value | 0;\n\t\t\tprevTop = element.scrollTop;\n\t\t};\n\n\t\tthis.maxScrollTop = () => maxTop;\n\t\tthis.maxScrollLeft = () => maxLeft;\n\n\t\tthis.disable = function() {\n\t\t\tnode = content.firstChild;\n\t\t\twhile (node) {\n\t\t\t\tnextNode = node.nextSibling;\n\t\t\t\telement.appendChild(node);\n\t\t\t\tnode = nextNode;\n\t\t\t}\n\t\t\tif (element === content.parentNode) { //in case disable() is called when it's already disabled.\n\t\t\t\telement.removeChild(content);\n\t\t\t}\n\t\t};\n\t\tthis.enable = function() {\n\t\t\tnode = element.firstChild;\n\t\t\tif (node === content) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\twhile (node) {\n\t\t\t\tnextNode = node.nextSibling;\n\t\t\t\tcontent.appendChild(node);\n\t\t\t\tnode = nextNode;\n\t\t\t}\n\t\t\telement.appendChild(content);\n\t\t\tthis.calibrate();\n\t\t};\n\t\tthis.calibrate = function(force) {\n\t\t\tlet widthMatches = (element.clientWidth === elementWidth),\n\t\t\t\tcs, x, y;\n\t\t\tprevTop = element.scrollTop;\n\t\t\tprevLeft = element.scrollLeft;\n\t\t\tif (widthMatches && element.clientHeight === elementHeight && content.offsetHeight === contentHeight && scrollWidth === element.scrollWidth && scrollHeight === element.scrollHeight && !force) {\n\t\t\t\treturn; //no need to recalculate things if the width and height haven't changed.\n\t\t\t}\n\t\t\tif (offsetTop || offsetLeft) {\n\t\t\t\tx = this.left();\n\t\t\t\ty = this.top();\n\t\t\t\tthis.left(-element.scrollLeft);\n\t\t\t\tthis.top(-element.scrollTop);\n\t\t\t}\n\t\t\tcs = _getComputedStyle(element);\n\t\t\t//first, we need to remove any width constraints to see how the content naturally flows so that we can see if it's wider than the containing element. If so, we've got to record the amount of overage so that we can apply that as padding in order for browsers to correctly handle things. Then we switch back to a width of 100% (without that, some browsers don't flow the content correctly)\n\t\t\tif (!widthMatches || force) {\n\t\t\t\tstyle.display = \"block\";\n\t\t\t\tstyle.width = \"auto\";\n\t\t\t\tstyle.paddingRight = \"0px\";\n\t\t\t\textraPadRight = Math.max(0, element.scrollWidth - element.clientWidth);\n\t\t\t\t//if the content is wider than the container, we need to add the paddingLeft and paddingRight in order for things to behave correctly.\n\t\t\t\tif (extraPadRight) {\n\t\t\t\t\textraPadRight += parseFloat(cs.paddingLeft) + (_addPaddingBR ? parseFloat(cs.paddingRight) : 0);\n\t\t\t\t}\n\t\t\t}\n\t\t\tstyle.display = \"inline-block\";\n\t\t\tstyle.position = \"relative\";\n\t\t\tstyle.overflow = \"visible\";\n\t\t\tstyle.verticalAlign = \"top\";\n\t\t\tstyle.boxSizing = \"content-box\";\n\t\t\tstyle.width = \"100%\";\n\t\t\tstyle.paddingRight = extraPadRight + \"px\";\n\t\t\t//some browsers neglect to factor in the bottom padding when calculating the scrollHeight, so we need to add that padding to the content when that happens. Allow a 2px margin for error\n\t\t\tif (_addPaddingBR) {\n\t\t\t\tstyle.paddingBottom = cs.paddingBottom;\n\t\t\t}\n\t\t\telementWidth = element.clientWidth;\n\t\t\telementHeight = element.clientHeight;\n\t\t\tscrollWidth = element.scrollWidth;\n\t\t\tscrollHeight = element.scrollHeight;\n\t\t\tmaxLeft = element.scrollWidth - elementWidth;\n\t\t\tmaxTop = element.scrollHeight - elementHeight;\n\t\t\tcontentHeight = content.offsetHeight;\n\t\t\tstyle.display = \"block\";\n\t\t\tif (x || y) {\n\t\t\t\tthis.left(x);\n\t\t\t\tthis.top(y);\n\t\t\t}\n\t\t};\n\t\tthis.content = content;\n\t\tthis.element = element;\n\t\tthis._skip = false;\n\t\tthis.enable();\n\t},\n\t_initCore = required => {\n\t\tif (_windowExists() && document.body) {\n\t\t\tlet nav = window && window.navigator;\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t\t_docElement = _doc.documentElement;\n\t\t\t_body = _doc.body;\n\t\t\t_tempDiv = _createElement(\"div\");\n\t\t\t_supportsPointer = !!window.PointerEvent;\n\t\t\t_placeholderDiv = _createElement(\"div\");\n\t\t\t_placeholderDiv.style.cssText = \"visibility:hidden;height:1px;top:-1px;pointer-events:none;position:relative;clear:both;cursor:grab\";\n\t\t\t_defaultCursor = _placeholderDiv.style.cursor === \"grab\" ? \"grab\" : \"move\";\n\t\t\t_isAndroid = (nav && nav.userAgent.toLowerCase().indexOf(\"android\") !== -1); //Android handles touch events in an odd way and it's virtually impossible to \"feature test\" so we resort to UA sniffing\n\t\t\t_isTouchDevice = ((\"ontouchstart\" in _docElement) && (\"orientation\" in _win)) || (nav && (nav.MaxTouchPoints > 0 || nav.msMaxTouchPoints > 0));\n\t\t\t_addPaddingBR = (function() { //this function is in charge of analyzing browser behavior related to padding. It sets the _addPaddingBR to true if the browser doesn't normally factor in the bottom or right padding on the element inside the scrolling area, and it sets _addPaddingLeft to true if it's a browser that requires the extra offset (offsetLeft) to be added to the paddingRight (like Opera).\n\t\t\t\tlet div = _createElement(\"div\"),\n\t\t\t\t\tchild = _createElement(\"div\"),\n\t\t\t\t\tchildStyle = child.style,\n\t\t\t\t\tparent = _body,\n\t\t\t\t\tval;\n\t\t\t\tchildStyle.display = \"inline-block\";\n\t\t\t\tchildStyle.position = \"relative\";\n\t\t\t\tdiv.style.cssText = \"width:90px;height:40px;padding:10px;overflow:auto;visibility:hidden\";\n\t\t\t\tdiv.appendChild(child);\n\t\t\t\tparent.appendChild(div);\n\t\t\t\tval = (child.offsetHeight + 18 > div.scrollHeight); //div.scrollHeight should be child.offsetHeight + 20 because of the 10px of padding on each side, but some browsers ignore one side. We allow a 2px margin of error.\n\t\t\t\tparent.removeChild(div);\n\t\t\t\treturn val;\n\t\t\t}());\n\t\t\t_touchEventLookup = (function(types) { //we create an object that makes it easy to translate touch event types into their \"pointer\" counterparts if we're in a browser that uses those instead. Like IE10 uses \"MSPointerDown\" instead of \"touchstart\", for example.\n\t\t\t\tlet standard = types.split(\",\"),\n\t\t\t\t\tconverted = (\"onpointerdown\" in _tempDiv ? \"pointerdown,pointermove,pointerup,pointercancel\" : \"onmspointerdown\" in _tempDiv ? \"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel\" : types).split(\",\"),\n\t\t\t\t\tobj = {},\n\t\t\t\t\ti = 4;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\tobj[standard[i]] = converted[i];\n\t\t\t\t\tobj[converted[i]] = standard[i];\n\t\t\t\t}\n\t\t\t\t//to avoid problems in iOS 9, test to see if the browser supports the \"passive\" option on addEventListener().\n\t\t\t\ttry {\n\t\t\t\t\t_docElement.addEventListener(\"test\", null, Object.defineProperty({}, \"passive\", {\n\t\t\t\t\t\tget: function () {\n\t\t\t\t\t\t\t_supportsPassive = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}));\n\t\t\t\t} catch (e) {}\n\t\t\t\treturn obj;\n\t\t\t}(\"touchstart,touchmove,touchend,touchcancel\"));\n\t\t\t_addListener(_doc, \"touchcancel\", _emptyFunc); //some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document. Very strange indeed.\n\t\t\t_addListener(_win, \"touchmove\", _emptyFunc); //works around Safari bugs that still allow the page to scroll even when we preventDefault() on the touchmove event.\n\t\t\t_body && _body.addEventListener(\"touchstart\", _emptyFunc); //works around Safari bug: https://gsap.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t\t_addListener(_doc, \"contextmenu\", function() {\n\t\t\t\tfor (let p in _lookup) {\n\t\t\t\t\tif (_lookup[p].isPressed) {\n\t\t\t\t\t\t_lookup[p].endDrag();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\tgsap = _coreInitted = _getGSAP();\n\t\t}\n\t\tif (gsap) {\n\t\t\tInertiaPlugin = gsap.plugins.inertia;\n\t\t\t_context = gsap.core.context || function() {};\n\t\t\t_checkPrefix = gsap.utils.checkPrefix;\n\t\t\t_transformProp = _checkPrefix(_transformProp);\n\t\t\t_transformOriginProp = _checkPrefix(_transformOriginProp);\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_getStyleSaver = gsap.core.getStyleSaver;\n\t\t\t_supports3D = !!_checkPrefix(\"perspective\");\n\t\t} else if (required) {\n\t\t\tconsole.warn(\"Please gsap.registerPlugin(Draggable)\");\n\t\t}\n\t};\n\n\n\n\n\n\nclass EventDispatcher {\n\n\tconstructor(target) {\n\t\tthis._listeners = {};\n\t\tthis.target = target || this;\n\t}\n\n\taddEventListener(type, callback) {\n\t\tlet list = this._listeners[type] || (this._listeners[type] = []);\n\t\tif (!~list.indexOf(callback)) {\n\t\t\tlist.push(callback);\n\t\t}\n\t}\n\n\tremoveEventListener(type, callback) {\n\t\tlet list = this._listeners[type],\n\t\t\ti = (list && list.indexOf(callback));\n\t\t(i >= 0) && list.splice(i, 1);\n\t}\n\n\tdispatchEvent(type) {\n\t\tlet result;\n\t\t(this._listeners[type] || []).forEach(callback => (callback.call(this, {type: type, target: this.target}) === false) && (result = false));\n\t\treturn result; //if any of the callbacks return false, pass that along.\n\t}\n}\n\n\n\n\n\n\n\n\n\nexport class Draggable extends EventDispatcher {\n\n\tconstructor(target, vars) {\n\t\tsuper();\n\t\t_coreInitted || _initCore(1);\n\t\ttarget = _toArray(target)[0]; //in case the target is a selector object or selector text\n\t\tthis.styles = _getStyleSaver && _getStyleSaver(target, \"transform,left,top\");\n\t\tif (!InertiaPlugin) {\n\t\t\tInertiaPlugin = gsap.plugins.inertia;\n\t\t}\n\t\tthis.vars = vars = _copy(vars || {});\n\t\tthis.target = target;\n\t\tthis.x = this.y = this.rotation = 0;\n\t\tthis.dragResistance = parseFloat(vars.dragResistance) || 0;\n\t\tthis.edgeResistance = isNaN(vars.edgeResistance) ? 1 : parseFloat(vars.edgeResistance) || 0;\n\t\tthis.lockAxis = vars.lockAxis;\n\t\tthis.autoScroll = vars.autoScroll || 0;\n\t\tthis.lockedAxis = null;\n\t\tthis.allowEventDefault = !!vars.allowEventDefault;\n\n\t\tgsap.getProperty(target, \"x\"); // to ensure that transforms are instantiated.\n\n\t\tlet type = (vars.type || \"x,y\").toLowerCase(),\n\t\t\txyMode = (~type.indexOf(\"x\") || ~type.indexOf(\"y\")),\n\t\t\trotationMode = (type.indexOf(\"rotation\") !== -1),\n\t\t\txProp = rotationMode ? \"rotation\" : xyMode ? \"x\" : \"left\",\n\t\t\tyProp = xyMode ? \"y\" : \"top\",\n\t\t\tallowX = !!(~type.indexOf(\"x\") || ~type.indexOf(\"left\") || type === \"scroll\"),\n\t\t\tallowY = !!(~type.indexOf(\"y\") || ~type.indexOf(\"top\") || type === \"scroll\"),\n\t\t\tminimumMovement = vars.minimumMovement || 2,\n\t\t\tself = this,\n\t\t\ttriggers = _toArray(vars.trigger || vars.handle || target),\n\t\t\tkillProps = {},\n\t\t\tdragEndTime = 0,\n\t\t\tcheckAutoScrollBounds = false,\n\t\t\tautoScrollMarginTop = vars.autoScrollMarginTop || 40,\n\t\t\tautoScrollMarginRight = vars.autoScrollMarginRight || 40,\n\t\t\tautoScrollMarginBottom = vars.autoScrollMarginBottom || 40,\n\t\t\tautoScrollMarginLeft = vars.autoScrollMarginLeft || 40,\n\t\t\tisClickable = vars.clickableTest || _isClickable,\n\t\t\tclickTime = 0,\n\t\t\tgsCache = target._gsap || gsap.core.getCache(target),\n\t\t\tisFixed = _isFixed(target),\n\t\t\tgetPropAsNum = (property, unit) => parseFloat(gsCache.get(target, property, unit)),\n\t\t\townerDoc = target.ownerDocument || _doc,\n\t\t\tenabled, scrollProxy, startPointerX, startPointerY, startElementX, startElementY, hasBounds, hasDragCallback, hasMoveCallback, maxX, minX, maxY, minY, touch, touchID, rotationOrigin, dirty, old, snapX, snapY, snapXY, isClicking, touchEventTarget, matrix, interrupted, allowNativeTouchScrolling, touchDragAxis, isDispatching, clickDispatch, trustedClickDispatch, isPreventingDefault, innerMatrix, dragged,\n\n\t\t\tonContextMenu = e => { //used to prevent long-touch from triggering a context menu.\n\t\t\t\t// (self.isPressed && e.which < 2) && self.endDrag() // previously ended drag when context menu was triggered, but instead we should just stop propagation and prevent the default event behavior.\n\t\t\t\t_preventDefault(e);\n\t\t\t\te.stopImmediatePropagation && e.stopImmediatePropagation();\n\t\t\t\treturn false;\n\t\t\t},\n\n\t\t\t//this method gets called on every tick of TweenLite.ticker which allows us to synchronize the renders to the core engine (which is typically synchronized with the display refresh via requestAnimationFrame). This is an optimization - it's better than applying the values inside the \"mousemove\" or \"touchmove\" event handler which may get called many times inbetween refreshes.\n\t\t\trender = suppressEvents => {\n\t\t\t\tif (self.autoScroll && self.isDragging && (checkAutoScrollBounds || dirty)) {\n\t\t\t\t\tlet e = target,\n\t\t\t\t\t\tautoScrollFactor = self.autoScroll * 15, //multiplying by 15 just gives us a better \"feel\" speed-wise.\n\t\t\t\t\t\tparent, isRoot, rect, pointerX, pointerY, changeX, changeY, gap;\n\t\t\t\t\tcheckAutoScrollBounds = false;\n\t\t\t\t\t_windowProxy.scrollTop = ((_win.pageYOffset != null) ? _win.pageYOffset : (ownerDoc.documentElement.scrollTop != null) ? ownerDoc.documentElement.scrollTop : ownerDoc.body.scrollTop);\n\t\t\t\t\t_windowProxy.scrollLeft = ((_win.pageXOffset != null) ? _win.pageXOffset : (ownerDoc.documentElement.scrollLeft != null) ? ownerDoc.documentElement.scrollLeft : ownerDoc.body.scrollLeft);\n\t\t\t\t\tpointerX = self.pointerX - _windowProxy.scrollLeft;\n\t\t\t\t\tpointerY = self.pointerY - _windowProxy.scrollTop;\n\t\t\t\t\twhile (e && !isRoot) { //walk up the chain and sense wherever the pointer is within 40px of an edge that's scrollable.\n\t\t\t\t\t\tisRoot = _isRoot(e.parentNode);\n\t\t\t\t\t\tparent = isRoot ? _windowProxy : e.parentNode;\n\t\t\t\t\t\trect = isRoot ? {bottom:Math.max(_docElement.clientHeight, _win.innerHeight || 0), right: Math.max(_docElement.clientWidth, _win.innerWidth || 0), left:0, top:0} : parent.getBoundingClientRect();\n\t\t\t\t\t\tchangeX = changeY = 0;\n\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\tgap = parent._gsMaxScrollY - parent.scrollTop;\n\t\t\t\t\t\t\tif (gap < 0) {\n\t\t\t\t\t\t\t\tchangeY = gap;\n\t\t\t\t\t\t\t} else if (pointerY > rect.bottom - autoScrollMarginBottom && gap) {\n\t\t\t\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t\t\t\t\tchangeY = Math.min(gap, (autoScrollFactor * (1 - Math.max(0, (rect.bottom - pointerY)) / autoScrollMarginBottom)) | 0);\n\t\t\t\t\t\t\t} else if (pointerY < rect.top + autoScrollMarginTop && parent.scrollTop) {\n\t\t\t\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t\t\t\t\tchangeY = -Math.min(parent.scrollTop, (autoScrollFactor * (1 - Math.max(0, (pointerY - rect.top)) / autoScrollMarginTop)) | 0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (changeY) {\n\t\t\t\t\t\t\t\tparent.scrollTop += changeY;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\tgap = parent._gsMaxScrollX - parent.scrollLeft;\n\t\t\t\t\t\t\tif (gap < 0) {\n\t\t\t\t\t\t\t\tchangeX = gap;\n\t\t\t\t\t\t\t} else if (pointerX > rect.right - autoScrollMarginRight && gap) {\n\t\t\t\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t\t\t\t\tchangeX = Math.min(gap, (autoScrollFactor * (1 - Math.max(0, (rect.right - pointerX)) / autoScrollMarginRight)) | 0);\n\t\t\t\t\t\t\t} else if (pointerX < rect.left + autoScrollMarginLeft && parent.scrollLeft) {\n\t\t\t\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t\t\t\t\tchangeX = -Math.min(parent.scrollLeft, (autoScrollFactor * (1 - Math.max(0, (pointerX - rect.left)) / autoScrollMarginLeft)) | 0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (changeX) {\n\t\t\t\t\t\t\t\tparent.scrollLeft += changeX;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (isRoot && (changeX || changeY)) {\n\t\t\t\t\t\t\t_win.scrollTo(parent.scrollLeft, parent.scrollTop);\n\t\t\t\t\t\t\tsetPointerPosition(self.pointerX + changeX, self.pointerY + changeY);\n\t\t\t\t\t\t}\n\t\t\t\t\t\te = parent;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (dirty) {\n\t\t\t\t\tlet {x, y} = self;\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.deltaX = x - parseFloat(gsCache.rotation);\n\t\t\t\t\t\tself.rotation = x;\n\t\t\t\t\t\tgsCache.rotation = x + \"deg\";\n\t\t\t\t\t\tgsCache.renderTransform(1, gsCache);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (scrollProxy) {\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tself.deltaY = y - scrollProxy.top();\n\t\t\t\t\t\t\t\tscrollProxy.top(y);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tself.deltaX = x - scrollProxy.left();\n\t\t\t\t\t\t\t\tscrollProxy.left(x);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (xyMode) {\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tself.deltaY = y - parseFloat(gsCache.y);\n\t\t\t\t\t\t\t\tgsCache.y = y + \"px\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tself.deltaX = x - parseFloat(gsCache.x);\n\t\t\t\t\t\t\t\tgsCache.x = x + \"px\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tgsCache.renderTransform(1, gsCache);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tself.deltaY = y - parseFloat(target.style.top || 0);\n\t\t\t\t\t\t\t\ttarget.style.top = y + \"px\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tself.deltaX = x - parseFloat(target.style.left || 0);\n\t\t\t\t\t\t\t\ttarget.style.left = x + \"px\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (hasDragCallback && !suppressEvents && !isDispatching) {\n\t\t\t\t\t\tisDispatching = true; //in case onDrag has an update() call (avoid endless loop)\n\t\t\t\t\t\tif (_dispatchEvent(self, \"drag\", \"onDrag\") === false) {\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tself.x -= self.deltaX;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tself.y -= self.deltaY;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\trender(true);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tisDispatching = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tdirty = false;\n\t\t\t},\n\n\t\t\t//copies the x/y from the element (whether that be transforms, top/left, or ScrollProxy's top/left) to the Draggable's x and y (and rotation if necessary) properties so that they reflect reality and it also (optionally) applies any snapping necessary. This is used by the InertiaPlugin tween in an onUpdate to ensure things are synced and snapped.\n\t\t\tsyncXY = (skipOnUpdate, skipSnap) => {\n\t\t\t\tlet { x, y } = self,\n\t\t\t\t\tsnappedValue, cs;\n\t\t\t\tif (!target._gsap) { //just in case the _gsap cache got wiped, like if the user called clearProps on the transform or something (very rare).\n\t\t\t\t\tgsCache = gsap.core.getCache(target);\n\t\t\t\t}\n\t\t\t\tgsCache.uncache && gsap.getProperty(target, \"x\"); // trigger a re-cache\n\t\t\t\tif (xyMode) {\n\t\t\t\t\tself.x = parseFloat(gsCache.x);\n\t\t\t\t\tself.y = parseFloat(gsCache.y);\n\t\t\t\t} else if (rotationMode) {\n\t\t\t\t\tself.x = self.rotation = parseFloat(gsCache.rotation);\n\t\t\t\t} else if (scrollProxy) {\n\t\t\t\t\tself.y = scrollProxy.top();\n\t\t\t\t\tself.x = scrollProxy.left();\n\t\t\t\t} else {\n\t\t\t\t\tself.y = parseFloat(target.style.top || ((cs = _getComputedStyle(target)) && cs.top)) || 0;\n\t\t\t\t\tself.x = parseFloat(target.style.left || (cs || {}).left) || 0;\n\t\t\t\t}\n\t\t\t\tif ((snapX || snapY || snapXY) && !skipSnap && (self.isDragging || self.isThrowing)) {\n\t\t\t\t\tif (snapXY) {\n\t\t\t\t\t\t_temp1.x = self.x;\n\t\t\t\t\t\t_temp1.y = self.y;\n\t\t\t\t\t\tsnappedValue = snapXY(_temp1);\n\t\t\t\t\t\tif (snappedValue.x !== self.x) {\n\t\t\t\t\t\t\tself.x = snappedValue.x;\n\t\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (snappedValue.y !== self.y) {\n\t\t\t\t\t\t\tself.y = snappedValue.y;\n\t\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (snapX) {\n\t\t\t\t\t\tsnappedValue = snapX(self.x);\n\t\t\t\t\t\tif (snappedValue !== self.x) {\n\t\t\t\t\t\t\tself.x = snappedValue;\n\t\t\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\t\t\tself.rotation = snappedValue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (snapY) {\n\t\t\t\t\t\tsnappedValue = snapY(self.y);\n\t\t\t\t\t\tif (snappedValue !== self.y) {\n\t\t\t\t\t\t\tself.y = snappedValue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tdirty && render(true);\n\t\t\t\tif (!skipOnUpdate) {\n\t\t\t\t\tself.deltaX = self.x - x;\n\t\t\t\t\tself.deltaY = self.y - y;\n\t\t\t\t\t_dispatchEvent(self, \"throwupdate\", \"onThrowUpdate\");\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tbuildSnapFunc = (snap, min, max, factor) => {\n\t\t\t\tif (min == null) {\n\t\t\t\t\tmin = -_bigNum;\n\t\t\t\t}\n\t\t\t\tif (max == null) {\n\t\t\t\t\tmax = _bigNum;\n\t\t\t\t}\n\t\t\t\tif (_isFunction(snap)) {\n\t\t\t\t\treturn n => {\n\t\t\t\t\t\tlet edgeTolerance = !self.isPressed ? 1 : 1 - self.edgeResistance; //if we're tweening, disable the edgeTolerance because it's already factored into the tweening values (we don't want to apply it multiple times)\n\t\t\t\t\t\treturn snap.call(self, (n > max ? max + (n - max) * edgeTolerance : (n < min) ? min + (n - min) * edgeTolerance : n) * factor) * factor;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (_isArray(snap)) {\n\t\t\t\t\treturn n => {\n\t\t\t\t\t\tlet i = snap.length,\n\t\t\t\t\t\t\tclosest = 0,\n\t\t\t\t\t\t\tabsDif = _bigNum,\n\t\t\t\t\t\t\tval, dif;\n\t\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\t\tval = snap[i];\n\t\t\t\t\t\t\tdif = val - n;\n\t\t\t\t\t\t\tif (dif < 0) {\n\t\t\t\t\t\t\t\tdif = -dif;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (dif < absDif && val >= min && val <= max) {\n\t\t\t\t\t\t\t\tclosest = i;\n\t\t\t\t\t\t\t\tabsDif = dif;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn snap[closest];\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\treturn isNaN(snap) ? n => n : () => snap * factor;\n\t\t\t},\n\n\t\t\tbuildPointSnapFunc = (snap, minX, maxX, minY, maxY, radius, factor) => {\n\t\t\t\tradius = (radius && radius < _bigNum) ? radius * radius : _bigNum; //so we don't have to Math.sqrt() in the functions. Performance optimization.\n\t\t\t\tif (_isFunction(snap)) {\n\t\t\t\t\treturn point => {\n\t\t\t\t\t\tlet edgeTolerance = !self.isPressed ? 1 : 1 - self.edgeResistance,\n\t\t\t\t\t\t\tx = point.x,\n\t\t\t\t\t\t\ty = point.y,\n\t\t\t\t\t\t\tresult, dx, dy; //if we're tweening, disable the edgeTolerance because it's already factored into the tweening values (we don't want to apply it multiple times)\n\t\t\t\t\t\tpoint.x = x = (x > maxX ? maxX + (x - maxX) * edgeTolerance : (x < minX) ? minX + (x - minX) * edgeTolerance : x);\n\t\t\t\t\t\tpoint.y = y = (y > maxY ? maxY + (y - maxY) * edgeTolerance : (y < minY) ? minY + (y - minY) * edgeTolerance : y);\n\t\t\t\t\t\tresult = snap.call(self, point);\n\t\t\t\t\t\tif (result !== point) {\n\t\t\t\t\t\t\tpoint.x = result.x;\n\t\t\t\t\t\t\tpoint.y = result.y;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (factor !== 1) {\n\t\t\t\t\t\t\tpoint.x *= factor;\n\t\t\t\t\t\t\tpoint.y *= factor;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (radius < _bigNum) {\n\t\t\t\t\t\t\tdx = point.x - x;\n\t\t\t\t\t\t\tdy = point.y - y;\n\t\t\t\t\t\t\tif (dx * dx + dy * dy > radius) {\n\t\t\t\t\t\t\t\tpoint.x = x;\n\t\t\t\t\t\t\t\tpoint.y = y;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn point;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (_isArray(snap)) {\n\t\t\t\t\treturn p => {\n\t\t\t\t\t\tlet i = snap.length,\n\t\t\t\t\t\t\tclosest = 0,\n\t\t\t\t\t\t\tminDist = _bigNum,\n\t\t\t\t\t\t\tx, y, point, dist;\n\t\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\t\tpoint = snap[i];\n\t\t\t\t\t\t\tx = point.x - p.x;\n\t\t\t\t\t\t\ty = point.y - p.y;\n\t\t\t\t\t\t\tdist = x * x + y * y;\n\t\t\t\t\t\t\tif (dist < minDist) {\n\t\t\t\t\t\t\t\tclosest = i;\n\t\t\t\t\t\t\t\tminDist = dist;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn (minDist <= radius) ? snap[closest] : p;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\treturn n => n;\n\t\t\t},\n\n\t\t\tcalculateBounds = () => {\n\t\t\t\tlet bounds, targetBounds, snap, snapIsRaw;\n\t\t\t\thasBounds = false;\n\t\t\t\tif (scrollProxy) {\n\t\t\t\t\tscrollProxy.calibrate();\n\t\t\t\t\tself.minX = minX = -scrollProxy.maxScrollLeft();\n\t\t\t\t\tself.minY = minY = -scrollProxy.maxScrollTop();\n\t\t\t\t\tself.maxX = maxX = self.maxY = maxY = 0;\n\t\t\t\t\thasBounds = true;\n\t\t\t\t} else if (!!vars.bounds) {\n\t\t\t\t\tbounds = _getBounds(vars.bounds, target.parentNode); //could be a selector/jQuery object or a DOM element or a generic object like {top:0, left:100, width:1000, height:800} or {minX:100, maxX:1100, minY:0, maxY:800}\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.minX = minX = bounds.left;\n\t\t\t\t\t\tself.maxX = maxX = bounds.left + bounds.width;\n\t\t\t\t\t\tself.minY = minY = self.maxY = maxY = 0;\n\t\t\t\t\t} else if (!_isUndefined(vars.bounds.maxX) || !_isUndefined(vars.bounds.maxY)) {\n\t\t\t\t\t\tbounds = vars.bounds;\n\t\t\t\t\t\tself.minX = minX = bounds.minX;\n\t\t\t\t\t\tself.minY = minY = bounds.minY;\n\t\t\t\t\t\tself.maxX = maxX = bounds.maxX;\n\t\t\t\t\t\tself.maxY = maxY = bounds.maxY;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttargetBounds = _getBounds(target, target.parentNode);\n\t\t\t\t\t\tself.minX = minX = Math.round(getPropAsNum(xProp, \"px\") + bounds.left - targetBounds.left);\n\t\t\t\t\t\tself.minY = minY = Math.round(getPropAsNum(yProp, \"px\") + bounds.top - targetBounds.top);\n\t\t\t\t\t\tself.maxX = maxX = Math.round(minX + (bounds.width - targetBounds.width));\n\t\t\t\t\t\tself.maxY = maxY = Math.round(minY + (bounds.height - targetBounds.height));\n\t\t\t\t\t}\n\t\t\t\t\tif (minX > maxX) {\n\t\t\t\t\t\tself.minX = maxX;\n\t\t\t\t\t\tself.maxX = maxX = minX;\n\t\t\t\t\t\tminX = self.minX;\n\t\t\t\t\t}\n\t\t\t\t\tif (minY > maxY) {\n\t\t\t\t\t\tself.minY = maxY;\n\t\t\t\t\t\tself.maxY = maxY = minY;\n\t\t\t\t\t\tminY = self.minY;\n\t\t\t\t\t}\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.minRotation = minX;\n\t\t\t\t\t\tself.maxRotation = maxX;\n\t\t\t\t\t}\n\t\t\t\t\thasBounds = true;\n\t\t\t\t}\n\t\t\t\tif (vars.liveSnap) {\n\t\t\t\t\tsnap = (vars.liveSnap === true) ? (vars.snap || {}) : vars.liveSnap;\n\t\t\t\t\tsnapIsRaw = (_isArray(snap) || _isFunction(snap));\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tsnapX = buildSnapFunc((snapIsRaw ? snap : snap.rotation), minX, maxX, 1);\n\t\t\t\t\t\tsnapY = null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (snap.points) {\n\t\t\t\t\t\t\tsnapXY = buildPointSnapFunc((snapIsRaw ? snap : snap.points), minX, maxX, minY, maxY, snap.radius, scrollProxy ? -1 : 1);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tsnapX = buildSnapFunc((snapIsRaw ? snap : snap.x || snap.left || snap.scrollLeft), minX, maxX, scrollProxy ? -1 : 1);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tsnapY = buildSnapFunc((snapIsRaw ? snap : snap.y || snap.top || snap.scrollTop), minY, maxY, scrollProxy ? -1 : 1);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonThrowComplete = () => {\n\t\t\t\tself.isThrowing = false;\n\t\t\t\t_dispatchEvent(self, \"throwcomplete\", \"onThrowComplete\");\n\t\t\t},\n\t\t\tonThrowInterrupt = () => {\n\t\t\t\tself.isThrowing = false;\n\t\t\t},\n\n\t\t\tanimate = (inertia, forceZeroVelocity) => {\n\t\t\t\tlet snap, snapIsRaw, tween, overshootTolerance;\n\t\t\t\tif (inertia && InertiaPlugin) {\n\t\t\t\t\tif (inertia === true) {\n\t\t\t\t\t\tsnap = vars.snap || vars.liveSnap || {};\n\t\t\t\t\t\tsnapIsRaw = (_isArray(snap) || _isFunction(snap));\n\t\t\t\t\t\tinertia = {resistance:(vars.throwResistance || vars.resistance || 1000) / (rotationMode ? 10 : 1)};\n\t\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\t\tinertia.rotation = _parseInertia(self, snapIsRaw ? snap : snap.rotation, maxX, minX, 1, forceZeroVelocity);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tinertia[xProp] = _parseInertia(self, snapIsRaw ? snap : snap.points || snap.x || snap.left, maxX, minX, scrollProxy ? -1 : 1, forceZeroVelocity || (self.lockedAxis === \"x\"));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tinertia[yProp] = _parseInertia(self, snapIsRaw ? snap : snap.points || snap.y || snap.top, maxY, minY, scrollProxy ? -1 : 1, forceZeroVelocity || (self.lockedAxis === \"y\"));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (snap.points || (_isArray(snap) && _isObject(snap[0]))) {\n\t\t\t\t\t\t\t\tinertia.linkedProps = xProp + \",\" + yProp;\n\t\t\t\t\t\t\t\tinertia.radius = snap.radius; //note: we also disable liveSnapping while throwing if there's a \"radius\" defined, otherwise it looks weird to have the item thrown past a snapping point but live-snapping mid-tween. We do this by altering the onUpdateParams so that \"skipSnap\" parameter is true for syncXY.\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tself.isThrowing = true;\n\t\t\t\t\tovershootTolerance = (!isNaN(vars.overshootTolerance)) ? vars.overshootTolerance : (vars.edgeResistance === 1) ? 0 : (1 - self.edgeResistance) + 0.2;\n\t\t\t\t\tif (!inertia.duration) {\n\t\t\t\t\t\tinertia.duration = {max: Math.max(vars.minDuration || 0, (\"maxDuration\" in vars) ? vars.maxDuration : 2), min: (!isNaN(vars.minDuration) ? vars.minDuration : (overshootTolerance === 0 || (_isObject(inertia) && inertia.resistance > 1000)) ? 0 : 0.5), overshoot: overshootTolerance};\n\t\t\t\t\t}\n\t\t\t\t\tself.tween = tween = gsap.to(scrollProxy || target, {\n\t\t\t\t\t\tinertia: inertia,\n\t\t\t\t\t\tdata: \"_draggable\",\n\t\t\t\t\t\tinherit: false,\n\t\t\t\t\t\tonComplete: onThrowComplete,\n\t\t\t\t\t\tonInterrupt: onThrowInterrupt,\n\t\t\t\t\t\tonUpdate: (vars.fastMode ? _dispatchEvent : syncXY),\n\t\t\t\t\t\tonUpdateParams: (vars.fastMode ? [self, \"onthrowupdate\", \"onThrowUpdate\"] : (snap && snap.radius) ? [false, true] : [])\n\t\t\t\t\t});\n\t\t\t\t\tif (!vars.fastMode) {\n\t\t\t\t\t\tif (scrollProxy) {\n\t\t\t\t\t\t\tscrollProxy._skip = true; // Microsoft browsers have a bug that causes them to briefly render the position incorrectly (it flashes to the end state when we seek() the tween even though we jump right back to the current position, and this only seems to happen when we're affecting both top and left), so we set a _suspendTransforms flag to prevent it from actually applying the values in the ScrollProxy.\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttween.render(1e9, true, true); // force to the end. Remember, the duration will likely change upon initting because that's when InertiaPlugin calculates it.\n\t\t\t\t\t\tsyncXY(true, true);\n\t\t\t\t\t\tself.endX = self.x;\n\t\t\t\t\t\tself.endY = self.y;\n\t\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\t\tself.endRotation = self.x;\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttween.play(0);\n\t\t\t\t\t\tsyncXY(true, true);\n\t\t\t\t\t\tif (scrollProxy) {\n\t\t\t\t\t\t\tscrollProxy._skip = false; //Microsoft browsers have a bug that causes them to briefly render the position incorrectly (it flashes to the end state when we seek() the tween even though we jump right back to the current position, and this only seems to happen when we're affecting both top and left), so we set a _suspendTransforms flag to prevent it from actually applying the values in the ScrollProxy.\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (hasBounds) {\n\t\t\t\t\tself.applyBounds();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tupdateMatrix = shiftStart => {\n\t\t\t\tlet start = matrix,\n\t\t\t\t\tp;\n\t\t\t\tmatrix = getGlobalMatrix(target.parentNode, true);\n\t\t\t\tif (shiftStart && self.isPressed && !matrix.equals(start || new Matrix2D())) { //if the matrix changes WHILE the element is pressed, we must adjust the startPointerX and startPointerY accordingly, so we invert the original matrix and figure out where the pointerX and pointerY were in the global space, then apply the new matrix to get the updated coordinates.\n\t\t\t\t\tp = start.inverse().apply({x:startPointerX, y:startPointerY});\n\t\t\t\t\tmatrix.apply(p, p);\n\t\t\t\t\tstartPointerX = p.x;\n\t\t\t\t\tstartPointerY = p.y;\n\t\t\t\t}\n\t\t\t\tif (matrix.equals(_identityMatrix)) { //if there are no transforms, we can optimize performance by not factoring in the matrix\n\t\t\t\t\tmatrix = null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\trecordStartPositions = () => {\n\t\t\t\tlet edgeTolerance = 1 - self.edgeResistance,\n\t\t\t\t\toffsetX = isFixed ? _getDocScrollLeft(ownerDoc) : 0,\n\t\t\t\t\toffsetY = isFixed ? _getDocScrollTop(ownerDoc) : 0,\n\t\t\t\t\tparsedOrigin, x, y;\n\t\t\t\tif (xyMode) { // in case the user set it as a different unit, like animating the x to \"100%\". We must convert it back to px!\n\t\t\t\t\tgsCache.x = getPropAsNum(xProp, \"px\") + \"px\";\n\t\t\t\t\tgsCache.y = getPropAsNum(yProp, \"px\") + \"px\";\n\t\t\t\t\tgsCache.renderTransform();\n\t\t\t\t}\n\t\t\t\tupdateMatrix(false);\n\t\t\t\t_point1.x = self.pointerX - offsetX;\n\t\t\t\t_point1.y = self.pointerY - offsetY;\n\t\t\t\tmatrix && matrix.apply(_point1, _point1);\n\t\t\t\tstartPointerX = _point1.x; //translate to local coordinate system\n\t\t\t\tstartPointerY = _point1.y;\n\t\t\t\tif (dirty) {\n\t\t\t\t\tsetPointerPosition(self.pointerX, self.pointerY);\n\t\t\t\t\trender(true);\n\t\t\t\t}\n\t\t\t\tinnerMatrix = getGlobalMatrix(target);\n\t\t\t\tif (scrollProxy) {\n\t\t\t\t\tcalculateBounds();\n\t\t\t\t\tstartElementY = scrollProxy.top();\n\t\t\t\t\tstartElementX = scrollProxy.left();\n\t\t\t\t} else {\n\t\t\t\t\t//if the element is in the process of tweening, don't force snapping to occur because it could make it jump. Imagine the user throwing, then before it's done, clicking on the element in its inbetween state.\n\t\t\t\t\tif (isTweening()) {\n\t\t\t\t\t\tsyncXY(true, true);\n\t\t\t\t\t\tcalculateBounds();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.applyBounds();\n\t\t\t\t\t}\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tparsedOrigin = target.ownerSVGElement ? [gsCache.xOrigin - target.getBBox().x, gsCache.yOrigin - target.getBBox().y] : (_getComputedStyle(target)[_transformOriginProp] || \"0 0\").split(\" \");\n\t\t\t\t\t\trotationOrigin = self.rotationOrigin = getGlobalMatrix(target).apply({x: parseFloat(parsedOrigin[0]) || 0, y: parseFloat(parsedOrigin[1]) || 0});\n\t\t\t\t\t\tsyncXY(true, true);\n\t\t\t\t\t\tx = self.pointerX - rotationOrigin.x - offsetX;\n\t\t\t\t\t\ty = rotationOrigin.y - self.pointerY + offsetY;\n\t\t\t\t\t\tstartElementX = self.x; //starting rotation (x always refers to rotation in type:\"rotation\", measured in degrees)\n\t\t\t\t\t\tstartElementY = self.y = Math.atan2(y, x) * _RAD2DEG;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t//parent = !isFixed && target.parentNode;\n\t\t\t\t\t\t//startScrollTop = parent ? parent.scrollTop || 0 : 0;\n\t\t\t\t\t\t//startScrollLeft = parent ? parent.scrollLeft || 0 : 0;\n\t\t\t\t\t\tstartElementY = getPropAsNum(yProp, \"px\"); //record the starting top and left values so that we can just add the mouse's movement to them later.\n\t\t\t\t\t\tstartElementX = getPropAsNum(xProp, \"px\");\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (hasBounds && edgeTolerance) {\n\t\t\t\t\tif (startElementX > maxX) {\n\t\t\t\t\t\tstartElementX = maxX + (startElementX - maxX) / edgeTolerance;\n\t\t\t\t\t} else if (startElementX < minX) {\n\t\t\t\t\t\tstartElementX = minX - (minX - startElementX) / edgeTolerance;\n\t\t\t\t\t}\n\t\t\t\t\tif (!rotationMode) {\n\t\t\t\t\t\tif (startElementY > maxY) {\n\t\t\t\t\t\t\tstartElementY = maxY + (startElementY - maxY) / edgeTolerance;\n\t\t\t\t\t\t} else if (startElementY < minY) {\n\t\t\t\t\t\t\tstartElementY = minY - (minY - startElementY) / edgeTolerance;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tself.startX = startElementX = _round(startElementX);\n\t\t\t\tself.startY = startElementY = _round(startElementY);\n\t\t\t},\n\n\t\t\tisTweening = () => self.tween && self.tween.isActive(),\n\n\t\t\tremovePlaceholder = () => {\n\t\t\t\tif (_placeholderDiv.parentNode && !isTweening() && !self.isDragging) { //_placeholderDiv just props open auto-scrolling containers so they don't collapse as the user drags left/up. We remove it after dragging (and throwing, if necessary) finishes.\n\t\t\t\t\t_placeholderDiv.parentNode.removeChild(_placeholderDiv);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t//called when the mouse is pressed (or touch starts)\n\t\t\tonPress = (e, force) => {\n\t\t\t\tlet i;\n\t\t\t\tif (!enabled || self.isPressed || !e || ((e.type === \"mousedown\" || e.type === \"pointerdown\") && !force && _getTime() - clickTime < 30 && _touchEventLookup[self.pointerEvent.type])) { //when we DON'T preventDefault() in order to accommodate touch-scrolling and the user just taps, many browsers also fire a mousedown/mouseup sequence AFTER the touchstart/touchend sequence, thus it'd result in two quick \"click\" events being dispatched. This line senses that condition and halts it on the subsequent mousedown.\n\t\t\t\t\tisPreventingDefault && e && enabled && _preventDefault(e); // in some browsers, we must listen for multiple event types like touchstart, pointerdown, mousedown. The first time this function is called, we record whether or not we _preventDefault() so that on duplicate calls, we can do the same if necessary.\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tinterrupted = isTweening();\n\t\t\t\tdragged = false; // we need to track whether or not it was dragged in this interaction so that if, for example, the user calls .endDrag() to FORCE it to stop and then they keep the mouse pressed down and eventually release, that would normally cause an onClick but we have to skip it in that case if there was dragging that occurred.\n\t\t\t\tself.pointerEvent = e;\n\t\t\t\tif (_touchEventLookup[e.type]) { //note: on iOS, BOTH touchmove and mousemove are dispatched, but the mousemove has pageY and pageX of 0 which would mess up the calculations and needlessly hurt performance.\n\t\t\t\t\ttouchEventTarget = ~e.type.indexOf(\"touch\") ? (e.currentTarget || e.target) : ownerDoc; //pointer-based touches (for Microsoft browsers) don't remain locked to the original target like other browsers, so we must use the document instead. The event type would be \"MSPointerDown\" or \"pointerdown\".\n\t\t\t\t\t_addListener(touchEventTarget, \"touchend\", onRelease);\n\t\t\t\t\t_addListener(touchEventTarget, \"touchmove\", onMove); // possible future change if PointerEvents are more standardized: https://developer.mozilla.org/en-US/docs/Web/API/Element/setPointerCapture\n\t\t\t\t\t_addListener(touchEventTarget, \"touchcancel\", onRelease);\n\t\t\t\t\t_addListener(ownerDoc, \"touchstart\", _onMultiTouchDocument);\n\t\t\t\t} else {\n\t\t\t\t\ttouchEventTarget = null;\n\t\t\t\t\t_addListener(ownerDoc, \"mousemove\", onMove); //attach these to the document instead of the box itself so that if the user's mouse moves too quickly (and off of the box), things still work.\n\t\t\t\t}\n\t\t\t\ttouchDragAxis = null;\n\t\t\t\tif (!_supportsPointer  || !touchEventTarget) {\n\t\t\t\t\t_addListener(ownerDoc, \"mouseup\", onRelease);\n\t\t\t\t\te && e.target && _addListener(e.target, \"mouseup\", onRelease); //we also have to listen directly on the element because some browsers don't bubble up the event to the _doc on elements with contentEditable=\"true\"\n\t\t\t\t}\n\t\t\t\tisClicking = (isClickable.call(self, e.target) && vars.dragClickables === false && !force);\n\t\t\t\tif (isClicking) {\n\t\t\t\t\t_addListener(e.target, \"change\", onRelease); //in some browsers, when you mousedown on a <select> element, no mouseup gets dispatched! So we listen for a \"change\" event instead.\n\t\t\t\t\t_dispatchEvent(self, \"pressInit\", \"onPressInit\");\n\t\t\t\t\t_dispatchEvent(self, \"press\", \"onPress\");\n\t\t\t\t\t_setSelectable(triggers, true); //accommodates things like inputs and elements with contentEditable=\"true\" (otherwise user couldn't drag to select text)\n\t\t\t\t\tisPreventingDefault = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tallowNativeTouchScrolling = (!touchEventTarget || allowX === allowY || self.vars.allowNativeTouchScrolling === false || (self.vars.allowContextMenu && e && (e.ctrlKey || e.which > 2))) ? false : allowX ? \"y\" : \"x\"; //note: in Chrome, right-clicking (for a context menu) fires onPress and it doesn't have the event.which set properly, so we must look for event.ctrlKey. If the user wants to allow context menus we should of course sense it here and not allow native touch scrolling.\n\t\t\t\tisPreventingDefault = !allowNativeTouchScrolling && !self.allowEventDefault;\n\t\t\t\tif (isPreventingDefault) {\n\t\t\t\t\t_preventDefault(e);\n\t\t\t\t\t_addListener(_win, \"touchforcechange\", _preventDefault); //works around safari bug: https://gsap.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t\t\t}\n\t\t\t\tif (e.changedTouches) { //touch events store the data slightly differently\n\t\t\t\t\te = touch = e.changedTouches[0];\n\t\t\t\t\ttouchID = e.identifier;\n\t\t\t\t} else if (e.pointerId) {\n\t\t\t\t\ttouchID = e.pointerId; //for some Microsoft browsers\n\t\t\t\t} else {\n\t\t\t\t\ttouch = touchID = null;\n\t\t\t\t}\n\t\t\t\t_dragCount++;\n\t\t\t\t_addToRenderQueue(render); //causes the Draggable to render on each \"tick\" of gsap.ticker (performance optimization - updating values in a mousemove can cause them to happen too frequently, like multiple times between frame redraws which is wasteful, and it also prevents values from updating properly in IE8)\n\t\t\t\tstartPointerY = self.pointerY = e.pageY; //record the starting x and y so that we can calculate the movement from the original in _onMouseMove\n\t\t\t\tstartPointerX = self.pointerX = e.pageX;\n\t\t\t\t_dispatchEvent(self, \"pressInit\", \"onPressInit\");\n\t\t\t\tif (allowNativeTouchScrolling || self.autoScroll) {\n\t\t\t\t\t_recordMaxScrolls(target.parentNode);\n\t\t\t\t}\n\t\t\t\tif (target.parentNode && self.autoScroll && !scrollProxy && !rotationMode && target.parentNode._gsMaxScrollX && !_placeholderDiv.parentNode && !target.getBBox) { //add a placeholder div to prevent the parent container from collapsing when the user drags the element left.\n\t\t\t\t\t_placeholderDiv.style.width = target.parentNode.scrollWidth + \"px\";\n\t\t\t\t\ttarget.parentNode.appendChild(_placeholderDiv);\n\t\t\t\t}\n\t\t\t\trecordStartPositions();\n\t\t\t\tself.tween && self.tween.kill();\n\t\t\t\tself.isThrowing = false;\n\t\t\t\tgsap.killTweensOf(scrollProxy || target, killProps, true); //in case the user tries to drag it before the last tween is done.\n\t\t\t\tscrollProxy && gsap.killTweensOf(target, {scrollTo:1}, true); //just in case the original target's scroll position is being tweened somewhere else.\n\t\t\t\tself.tween = self.lockedAxis = null;\n\t\t\t\tif (vars.zIndexBoost || (!rotationMode && !scrollProxy && vars.zIndexBoost !== false)) {\n\t\t\t\t\ttarget.style.zIndex = Draggable.zIndex++;\n\t\t\t\t}\n\t\t\t\tself.isPressed = true;\n\t\t\t\thasDragCallback = !!(vars.onDrag || self._listeners.drag);\n\t\t\t\thasMoveCallback = !!(vars.onMove || self._listeners.move);\n\t\t\t\tif (vars.cursor !== false || vars.activeCursor) {\n\t\t\t\t\ti = triggers.length;\n\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\tgsap.set(triggers[i], {cursor: vars.activeCursor || vars.cursor || (_defaultCursor === \"grab\" ? \"grabbing\" : _defaultCursor)});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t_dispatchEvent(self, \"press\", \"onPress\");\n\t\t\t},\n\n\t\t\t//called every time the mouse/touch moves\n\t\t\tonMove = e => {\n\t\t\t\tlet originalEvent = e,\n\t\t\t\t\ttouches, pointerX, pointerY, i, dx, dy;\n\t\t\t\tif (!enabled || _isMultiTouching || !self.isPressed || !e) {\n\t\t\t\t\tisPreventingDefault && e && enabled && _preventDefault(e); // in some browsers, we must listen for multiple event types like touchmove, pointermove, mousemove. The first time this function is called, we record whether or not we _preventDefault() so that on duplicate calls, we can do the same if necessary.\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tself.pointerEvent = e;\n\t\t\t\ttouches = e.changedTouches;\n\t\t\t\tif (touches) { //touch events store the data slightly differently\n\t\t\t\t\te = touches[0];\n\t\t\t\t\tif (e !== touch && e.identifier !== touchID) { //Usually changedTouches[0] will be what we're looking for, but in case it's not, look through the rest of the array...(and Android browsers don't reuse the event like iOS)\n\t\t\t\t\t\ti = touches.length;\n\t\t\t\t\t\twhile (--i > -1 && (e = touches[i]).identifier !== touchID && e.target !== target) {} // Some Android devices dispatch a touchstart AND pointerdown initially, and then only pointermove thus the touchID may not match because it was grabbed from the touchstart event whereas the pointer event is the one that the browser dispatches for move, so if the event target matches this Draggable's target, let it through.\n\t\t\t\t\t\tif (i < 0) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (e.pointerId && touchID && e.pointerId !== touchID) { //for some Microsoft browsers, we must attach the listener to the doc rather than the trigger so that when the finger moves outside the bounds of the trigger, things still work. So if the event we're receiving has a pointerId that doesn't match the touchID, ignore it (for multi-touch)\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (touchEventTarget && allowNativeTouchScrolling && !touchDragAxis) { //Android browsers force us to decide on the first \"touchmove\" event if we should allow the default (scrolling) behavior or preventDefault(). Otherwise, a \"touchcancel\" will be fired and then no \"touchmove\" or \"touchend\" will fire during the scrolling (no good).\n\t\t\t\t\t_point1.x = e.pageX - (isFixed ? _getDocScrollLeft(ownerDoc) : 0);\n\t\t\t\t\t_point1.y = e.pageY - (isFixed ? _getDocScrollTop(ownerDoc) : 0);\n\t\t\t\t\tmatrix && matrix.apply(_point1, _point1);\n\t\t\t\t\tpointerX = _point1.x;\n\t\t\t\t\tpointerY = _point1.y;\n\t\t\t\t\tdx = Math.abs(pointerX - startPointerX);\n\t\t\t\t\tdy = Math.abs(pointerY - startPointerY);\n\t\t\t\t\tif ((dx !== dy && (dx > minimumMovement || dy > minimumMovement)) || (_isAndroid && allowNativeTouchScrolling === touchDragAxis)) {\n\t\t\t\t\t\ttouchDragAxis = (dx > dy && allowX) ? \"x\" : \"y\";\n\t\t\t\t\t\tif (allowNativeTouchScrolling && touchDragAxis !== allowNativeTouchScrolling) {\n\t\t\t\t\t\t\t_addListener(_win, \"touchforcechange\", _preventDefault); // prevents native touch scrolling from taking over if the user started dragging in the other direction in iOS Safari\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (self.vars.lockAxisOnTouchScroll !== false && allowX && allowY) {\n\t\t\t\t\t\t\tself.lockedAxis = (touchDragAxis === \"x\") ? \"y\" : \"x\";\n\t\t\t\t\t\t\t_isFunction(self.vars.onLockAxis) && self.vars.onLockAxis.call(self, originalEvent);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (_isAndroid && allowNativeTouchScrolling === touchDragAxis) {\n\t\t\t\t\t\t\tonRelease(originalEvent);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!self.allowEventDefault && (!allowNativeTouchScrolling || (touchDragAxis && allowNativeTouchScrolling !== touchDragAxis)) && originalEvent.cancelable !== false) {\n\t\t\t\t\t_preventDefault(originalEvent);\n\t\t\t\t\tisPreventingDefault = true;\n\t\t\t\t} else if (isPreventingDefault) {\n\t\t\t\t\tisPreventingDefault = false;\n\t\t\t\t}\n\n\t\t\t\tif (self.autoScroll) {\n\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t}\n\t\t\t\tsetPointerPosition(e.pageX, e.pageY, hasMoveCallback);\n\t\t\t},\n\n\t\t\tsetPointerPosition = (pointerX, pointerY, invokeOnMove) => {\n\t\t\t\tlet dragTolerance = 1 - self.dragResistance,\n\t\t\t\t\tedgeTolerance = 1 - self.edgeResistance,\n\t\t\t\t\tprevPointerX = self.pointerX,\n\t\t\t\t\tprevPointerY = self.pointerY,\n\t\t\t\t\tprevStartElementY = startElementY,\n\t\t\t\t\tprevX = self.x,\n\t\t\t\t\tprevY = self.y,\n\t\t\t\t\tprevEndX = self.endX,\n\t\t\t\t\tprevEndY = self.endY,\n\t\t\t\t\tprevEndRotation = self.endRotation,\n\t\t\t\t\tprevDirty = dirty,\n\t\t\t\t\txChange, yChange, x, y, dif, temp;\n\t\t\t\tself.pointerX = pointerX;\n\t\t\t\tself.pointerY = pointerY;\n\t\t\t\tif (isFixed) {\n\t\t\t\t\tpointerX -= _getDocScrollLeft(ownerDoc);\n\t\t\t\t\tpointerY -= _getDocScrollTop(ownerDoc);\n\t\t\t\t}\n\t\t\t\tif (rotationMode) {\n\t\t\t\t\ty = Math.atan2(rotationOrigin.y - pointerY, pointerX - rotationOrigin.x) * _RAD2DEG;\n\t\t\t\t\tdif = self.y - y;\n\t\t\t\t\tif (dif > 180) {\n\t\t\t\t\t\tstartElementY -= 360;\n\t\t\t\t\t\tself.y = y;\n\t\t\t\t\t} else if (dif < -180) {\n\t\t\t\t\t\tstartElementY += 360;\n\t\t\t\t\t\tself.y = y;\n\t\t\t\t\t}\n\t\t\t\t\tif (self.x !== startElementX || Math.max(Math.abs(startPointerX - pointerX), Math.abs(startPointerY - pointerY)) > minimumMovement) {\n\t\t\t\t\t\tself.y = y;\n\t\t\t\t\t\tx = startElementX + (startElementY - y) * dragTolerance;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tx = startElementX;\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\t\t\t\t\tif (matrix) {\n\t\t\t\t\t\ttemp = pointerX * matrix.a + pointerY * matrix.c + matrix.e;\n\t\t\t\t\t\tpointerY = pointerX * matrix.b + pointerY * matrix.d + matrix.f;\n\t\t\t\t\t\tpointerX = temp;\n\t\t\t\t\t}\n\t\t\t\t\tyChange = (pointerY - startPointerY);\n\t\t\t\t\txChange = (pointerX - startPointerX);\n\t\t\t\t\tif (yChange < minimumMovement && yChange > -minimumMovement) {\n\t\t\t\t\t\tyChange = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (xChange < minimumMovement && xChange > -minimumMovement) {\n\t\t\t\t\t\txChange = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif ((self.lockAxis || self.lockedAxis) && (xChange || yChange)) {\n\t\t\t\t\t\ttemp = self.lockedAxis;\n\t\t\t\t\t\tif (!temp) {\n\t\t\t\t\t\t\tself.lockedAxis = temp = (allowX && Math.abs(xChange) > Math.abs(yChange)) ? \"y\" : allowY ? \"x\" : null;\n\t\t\t\t\t\t\tif (temp && _isFunction(self.vars.onLockAxis)) {\n\t\t\t\t\t\t\t\tself.vars.onLockAxis.call(self, self.pointerEvent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (temp === \"y\") {\n\t\t\t\t\t\t\tyChange = 0;\n\t\t\t\t\t\t} else if (temp === \"x\") {\n\t\t\t\t\t\t\txChange = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tx = _round(startElementX + xChange * dragTolerance);\n\t\t\t\t\ty = _round(startElementY + yChange * dragTolerance);\n\t\t\t\t}\n\n\t\t\t\tif ((snapX || snapY || snapXY) && (self.x !== x || (self.y !== y && !rotationMode))) {\n\t\t\t\t\tif (snapXY) {\n\t\t\t\t\t\t_temp1.x = x;\n\t\t\t\t\t\t_temp1.y = y;\n\t\t\t\t\t\ttemp = snapXY(_temp1);\n\t\t\t\t\t\tx = _round(temp.x);\n\t\t\t\t\t\ty = _round(temp.y);\n\t\t\t\t\t}\n\t\t\t\t\tif (snapX) {\n\t\t\t\t\t\tx = _round(snapX(x));\n\t\t\t\t\t}\n\t\t\t\t\tif (snapY) {\n\t\t\t\t\t\ty = _round(snapY(y));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (hasBounds) {\n\t\t\t\t\tif (x > maxX) {\n\t\t\t\t\t\tx = maxX + Math.round((x - maxX) * edgeTolerance);\n\t\t\t\t\t} else if (x < minX) {\n\t\t\t\t\t\tx = minX + Math.round((x - minX) * edgeTolerance);\n\t\t\t\t\t}\n\t\t\t\t\tif (!rotationMode) {\n\t\t\t\t\t\tif (y > maxY) {\n\t\t\t\t\t\t\ty = Math.round(maxY + (y - maxY) * edgeTolerance);\n\t\t\t\t\t\t} else if (y < minY) {\n\t\t\t\t\t\t\ty = Math.round(minY + (y - minY) * edgeTolerance);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (self.x !== x || (self.y !== y && !rotationMode)) {\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.endRotation = self.x = self.endX = x;\n\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\tself.y = self.endY = y;\n\t\t\t\t\t\t\tdirty = true; //a flag that indicates we need to render the target next time the TweenLite.ticker dispatches a \"tick\" event (typically on a requestAnimationFrame) - this is a performance optimization (we shouldn't render on every move because sometimes many move events can get dispatched between screen refreshes, and that'd be wasteful to render every time)\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\tself.x = self.endX = x;\n\t\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (!invokeOnMove || _dispatchEvent(self, \"move\", \"onMove\") !== false) {\n\t\t\t\t\t\tif (!self.isDragging && self.isPressed) {\n\t\t\t\t\t\t\tself.isDragging = dragged = true;\n\t\t\t\t\t\t\t_dispatchEvent(self, \"dragstart\", \"onDragStart\");\n\t\t\t\t\t\t}\n\t\t\t\t\t} else { //revert because the onMove returned false!\n\t\t\t\t\t\tself.pointerX = prevPointerX;\n\t\t\t\t\t\tself.pointerY = prevPointerY;\n\t\t\t\t\t\tstartElementY = prevStartElementY;\n\t\t\t\t\t\tself.x = prevX;\n\t\t\t\t\t\tself.y = prevY;\n\t\t\t\t\t\tself.endX = prevEndX;\n\t\t\t\t\t\tself.endY = prevEndY;\n\t\t\t\t\t\tself.endRotation = prevEndRotation;\n\t\t\t\t\t\tdirty = prevDirty;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t//called when the mouse/touch is released\n\t\t\tonRelease = (e, force) => {\n\t\t\t\tif (!enabled || !self.isPressed || (e && touchID != null && !force && ((e.pointerId && e.pointerId !== touchID && e.target !== target) || (e.changedTouches && !_hasTouchID(e.changedTouches, touchID))))) {  //for some Microsoft browsers, we must attach the listener to the doc rather than the trigger so that when the finger moves outside the bounds of the trigger, things still work. So if the event we're receiving has a pointerId that doesn't match the touchID, ignore it (for multi-touch)\n\t\t\t\t\tisPreventingDefault && e && enabled && _preventDefault(e); // in some browsers, we must listen for multiple event types like touchend, pointerup, mouseup. The first time this function is called, we record whether or not we _preventDefault() so that on duplicate calls, we can do the same if necessary.\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tself.isPressed = false;\n\t\t\t\tlet originalEvent = e,\n\t\t\t\t\twasDragging = self.isDragging,\n\t\t\t\t\tisContextMenuRelease = (self.vars.allowContextMenu && e && (e.ctrlKey || e.which > 2)),\n\t\t\t\t\tplaceholderDelayedCall = gsap.delayedCall(0.001, removePlaceholder),\n\t\t\t\t\ttouches, i, syntheticEvent, eventTarget, syntheticClick;\n\t\t\t\tif (touchEventTarget) {\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchend\", onRelease);\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchmove\", onMove);\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchcancel\", onRelease);\n\t\t\t\t\t_removeListener(ownerDoc, \"touchstart\", _onMultiTouchDocument);\n\t\t\t\t} else {\n\t\t\t\t\t_removeListener(ownerDoc, \"mousemove\", onMove);\n\t\t\t\t}\n\t\t\t\t_removeListener(_win, \"touchforcechange\", _preventDefault);\n\t\t\t\tif (!_supportsPointer || !touchEventTarget) {\n\t\t\t\t\t_removeListener(ownerDoc, \"mouseup\", onRelease);\n\t\t\t\t\te && e.target && _removeListener(e.target, \"mouseup\", onRelease);\n\t\t\t\t}\n\t\t\t\tdirty = false;\n\t\t\t\tif (wasDragging) {\n\t\t\t\t\tdragEndTime = _lastDragTime = _getTime();\n\t\t\t\t\tself.isDragging = false;\n\t\t\t\t}\n\t\t\t\t_removeFromRenderQueue(render);\n\t\t\t\tif (isClicking && !isContextMenuRelease) {\n\t\t\t\t\tif (e) {\n\t\t\t\t\t\t_removeListener(e.target, \"change\", onRelease);\n\t\t\t\t\t\tself.pointerEvent = originalEvent;\n\t\t\t\t\t}\n\t\t\t\t\t_setSelectable(triggers, false);\n\t\t\t\t\t_dispatchEvent(self, \"release\", \"onRelease\");\n\t\t\t\t\t_dispatchEvent(self, \"click\", \"onClick\");\n\t\t\t\t\tisClicking = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\ti = triggers.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t_setStyle(triggers[i], \"cursor\", vars.cursor || (vars.cursor !== false ? _defaultCursor : null));\n\t\t\t\t}\n\t\t\t\t_dragCount--;\n\t\t\t\tif (e) {\n\t\t\t\t\ttouches = e.changedTouches;\n\t\t\t\t\tif (touches) { //touch events store the data slightly differently\n\t\t\t\t\t\te = touches[0];\n\t\t\t\t\t\tif (e !== touch && e.identifier !== touchID) { //Usually changedTouches[0] will be what we're looking for, but in case it's not, look through the rest of the array...(and Android browsers don't reuse the event like iOS)\n\t\t\t\t\t\t\ti = touches.length;\n\t\t\t\t\t\t\twhile (--i > -1 && (e = touches[i]).identifier !== touchID && e.target !== target) {}\n\t\t\t\t\t\t\tif (i < 0 && !force) {\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tself.pointerEvent = originalEvent;\n\t\t\t\t\tself.pointerX = e.pageX;\n\t\t\t\t\tself.pointerY = e.pageY;\n\t\t\t\t}\n\t\t\t\tif (isContextMenuRelease && originalEvent) {\n\t\t\t\t\t_preventDefault(originalEvent);\n\t\t\t\t\tisPreventingDefault = true;\n\t\t\t\t\t_dispatchEvent(self, \"release\", \"onRelease\");\n\t\t\t\t} else if (originalEvent && !wasDragging) {\n\t\t\t\t\tisPreventingDefault = false;\n\t\t\t\t\tif (interrupted && (vars.snap || vars.bounds)) { //otherwise, if the user clicks on the object while it's animating to a snapped position, and then releases without moving 3 pixels, it will just stay there (it should animate/snap)\n\t\t\t\t\t\tanimate(vars.inertia || vars.throwProps);\n\t\t\t\t\t}\n\t\t\t\t\t_dispatchEvent(self, \"release\", \"onRelease\");\n\t\t\t\t\tif ((!_isAndroid || originalEvent.type !== \"touchmove\") && originalEvent.type.indexOf(\"cancel\") === -1) { //to accommodate native scrolling on Android devices, we have to immediately call onRelease() on the first touchmove event, but that shouldn't trigger a \"click\".\n\t\t\t\t\t\t_dispatchEvent(self, \"click\", \"onClick\");\n\t\t\t\t\t\tif (_getTime() - clickTime < 300) {\n\t\t\t\t\t\t\t_dispatchEvent(self, \"doubleclick\", \"onDoubleClick\");\n\t\t\t\t\t\t}\n\t\t\t\t\t\teventTarget = originalEvent.target || target; //old IE uses srcElement\n\t\t\t\t\t\tclickTime = _getTime();\n\t\t\t\t\t\tsyntheticClick = () => { // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n\t\t\t\t\t\t\tif (clickTime !== clickDispatch && self.enabled() && !self.isPressed && !originalEvent.defaultPrevented) {\n\t\t\t\t\t\t\t\tif (eventTarget.click) { //some browsers (like mobile Safari) don't properly trigger the click event\n\t\t\t\t\t\t\t\t\teventTarget.click();\n\t\t\t\t\t\t\t\t} else if (ownerDoc.createEvent) {\n\t\t\t\t\t\t\t\t\tsyntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n\t\t\t\t\t\t\t\t\tsyntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, self.pointerEvent.screenX, self.pointerEvent.screenY, self.pointerX, self.pointerY, false, false, false, false, 0, null);\n\t\t\t\t\t\t\t\t\teventTarget.dispatchEvent(syntheticEvent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (!_isAndroid && !originalEvent.defaultPrevented) { //iOS Safari requires the synthetic click to happen immediately or else it simply won't work, but Android doesn't play nice.\n\t\t\t\t\t\t\tgsap.delayedCall(0.05, syntheticClick); //in addition to the iOS bug workaround, there's a Firefox issue with clicking on things like a video to play, so we must fake a click event in a slightly delayed fashion. Previously, we listened for the \"click\" event with \"capture\" false which solved the video-click-to-play issue, but it would allow the \"click\" event to be dispatched twice like if you were using a jQuery.click() because that was handled in the capture phase, thus we had to switch to the capture phase to avoid the double-dispatching, but do the delayed synthetic click. Don't fire it too fast (like 0.00001) because we want to give the native event a chance to fire first as it's \"trusted\".\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tanimate(vars.inertia || vars.throwProps); //will skip if inertia/throwProps isn't defined or InertiaPlugin isn't loaded.\n\t\t\t\t\tif (!self.allowEventDefault && originalEvent && (vars.dragClickables !== false || !isClickable.call(self, originalEvent.target)) && wasDragging && (!allowNativeTouchScrolling || (touchDragAxis && allowNativeTouchScrolling === touchDragAxis)) && originalEvent.cancelable !== false) {\n\t\t\t\t\t\tisPreventingDefault = true;\n\t\t\t\t\t\t_preventDefault(originalEvent);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tisPreventingDefault = false;\n\t\t\t\t\t}\n\t\t\t\t\t_dispatchEvent(self, \"release\", \"onRelease\");\n\t\t\t\t}\n\t\t\t\tisTweening() && placeholderDelayedCall.duration( self.tween.duration() ); //sync the timing so that the placeholder DIV gets\n\t\t\t\twasDragging && _dispatchEvent(self, \"dragend\", \"onDragEnd\");\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\tupdateScroll = e => {\n\t\t\t\tif (e && self.isDragging && !scrollProxy) {\n\t\t\t\t\tlet parent = e.target || target.parentNode,\n\t\t\t\t\t\tdeltaX = parent.scrollLeft - parent._gsScrollX,\n\t\t\t\t\t\tdeltaY = parent.scrollTop - parent._gsScrollY;\n\t\t\t\t\tif (deltaX || deltaY) {\n\t\t\t\t\t\tif (matrix) {\n\t\t\t\t\t\t\tstartPointerX -= deltaX * matrix.a + deltaY * matrix.c;\n\t\t\t\t\t\t\tstartPointerY -= deltaY * matrix.d + deltaX * matrix.b;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tstartPointerX -= deltaX;\n\t\t\t\t\t\t\tstartPointerY -= deltaY;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tparent._gsScrollX += deltaX;\n\t\t\t\t\t\tparent._gsScrollY += deltaY;\n\t\t\t\t\t\tsetPointerPosition(self.pointerX, self.pointerY);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonClick = e => { //this was a huge pain in the neck to align all the various browsers and their behaviors. Chrome, Firefox, Safari, Opera, Android, and Microsoft Edge all handle events differently! Some will only trigger native behavior (like checkbox toggling) from trusted events. Others don't even support isTrusted, but require 2 events to flow through before triggering native behavior. Edge treats everything as trusted but also mandates that 2 flow through to trigger the correct native behavior.\n\t\t\t\tlet time = _getTime(),\n\t\t\t\t\trecentlyClicked = (time - clickTime < 100),\n\t\t\t\t\trecentlyDragged = (time - dragEndTime < 50),\n\t\t\t\t\talreadyDispatched = (recentlyClicked && clickDispatch === clickTime),\n\t\t\t\t\tdefaultPrevented = (self.pointerEvent && self.pointerEvent.defaultPrevented),\n\t\t\t\t\talreadyDispatchedTrusted = (recentlyClicked && trustedClickDispatch === clickTime),\n\t\t\t\t\ttrusted = e.isTrusted || (e.isTrusted == null && recentlyClicked && alreadyDispatched); //note: Safari doesn't support isTrusted, and it won't properly execute native behavior (like toggling checkboxes) on the first synthetic \"click\" event - we must wait for the 2nd and treat it as trusted (but stop propagation at that point). Confusing, I know. Don't you love cross-browser compatibility challenges?\n\t\t\t\tif ((alreadyDispatched || (recentlyDragged && self.vars.suppressClickOnDrag !== false) ) && e.stopImmediatePropagation) {\n\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t}\n\t\t\t\tif (recentlyClicked && !(self.pointerEvent && self.pointerEvent.defaultPrevented) && (!alreadyDispatched || (trusted && !alreadyDispatchedTrusted))) { //let the first click pass through unhindered. Let the next one only if it's trusted, then no more (stop quick-succession ones)\n\t\t\t\t\tif (trusted && alreadyDispatched) {\n\t\t\t\t\t\ttrustedClickDispatch = clickTime;\n\t\t\t\t\t}\n\t\t\t\t\tclickDispatch = clickTime;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (self.isPressed || recentlyDragged || recentlyClicked) {\n\t\t\t\t\tif (!trusted || !e.detail || !recentlyClicked || defaultPrevented) {\n\t\t\t\t\t\t_preventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!recentlyClicked && !recentlyDragged && !dragged) { // for script-triggered event dispatches, like element.click()\n\t\t\t\t\te && e.target && (self.pointerEvent = e);\n\t\t\t\t\t_dispatchEvent(self, \"click\", \"onClick\");\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tlocalizePoint = p => matrix ? {x:p.x * matrix.a + p.y * matrix.c + matrix.e, y:p.x * matrix.b + p.y * matrix.d + matrix.f} : {x:p.x, y:p.y};\n\n\t\told = Draggable.get(target);\n\t\told && old.kill(); // avoids duplicates (an element can only be controlled by one Draggable)\n\n\t\t//give the user access to start/stop dragging...\n\t\tthis.startDrag = (event, align) => {\n\t\t\tlet r1, r2, p1, p2;\n\t\t\tonPress(event || self.pointerEvent, true);\n\t\t\t//if the pointer isn't on top of the element, adjust things accordingly\n\t\t\tif (align && !self.hitTest(event || self.pointerEvent)) {\n\t\t\t\tr1 = _parseRect(event || self.pointerEvent);\n\t\t\t\tr2 = _parseRect(target);\n\t\t\t\tp1 = localizePoint({x:r1.left + r1.width / 2, y:r1.top + r1.height / 2});\n\t\t\t\tp2 = localizePoint({x:r2.left + r2.width / 2, y:r2.top + r2.height / 2});\n\t\t\t\tstartPointerX -= p1.x - p2.x;\n\t\t\t\tstartPointerY -= p1.y - p2.y;\n\t\t\t}\n\t\t\tif (!self.isDragging) {\n\t\t\t\tself.isDragging = dragged = true;\n\t\t\t\t_dispatchEvent(self, \"dragstart\", \"onDragStart\");\n\t\t\t}\n\t\t};\n\t\tthis.drag = onMove;\n\t\tthis.endDrag = e =>\tonRelease(e || self.pointerEvent, true);\n\t\tthis.timeSinceDrag = () => self.isDragging ? 0 : (_getTime() - dragEndTime) / 1000;\n\t\tthis.timeSinceClick = () => (_getTime() - clickTime) / 1000;\n\t\tthis.hitTest = (target, threshold) => Draggable.hitTest(self.target, target, threshold);\n\n\t\tthis.getDirection = (from, diagonalThreshold) => { //from can be \"start\" (default), \"velocity\", or an element\n\t\t\tlet mode = (from === \"velocity\" && InertiaPlugin) ? from : (_isObject(from) && !rotationMode) ? \"element\" : \"start\",\n\t\t\t\txChange, yChange, ratio, direction, r1, r2;\n\t\t\tif (mode === \"element\") {\n\t\t\t\tr1 = _parseRect(self.target);\n\t\t\t\tr2 = _parseRect(from);\n\t\t\t}\n\t\t\txChange = (mode === \"start\") ? self.x - startElementX : (mode === \"velocity\") ? InertiaPlugin.getVelocity(target, xProp) : (r1.left + r1.width / 2) - (r2.left + r2.width / 2);\n\t\t\tif (rotationMode) {\n\t\t\t\treturn xChange < 0 ? \"counter-clockwise\" : \"clockwise\";\n\t\t\t} else {\n\t\t\t\tdiagonalThreshold = diagonalThreshold || 2;\n\t\t\t\tyChange = (mode === \"start\") ? self.y - startElementY : (mode === \"velocity\") ? InertiaPlugin.getVelocity(target, yProp) : (r1.top + r1.height / 2) - (r2.top + r2.height / 2);\n\t\t\t\tratio = Math.abs(xChange / yChange);\n\t\t\t\tdirection = (ratio < 1 / diagonalThreshold) ? \"\" : (xChange < 0) ? \"left\" : \"right\";\n\t\t\t\tif (ratio < diagonalThreshold) {\n\t\t\t\t\tif (direction !== \"\") {\n\t\t\t\t\t\tdirection += \"-\";\n\t\t\t\t\t}\n\t\t\t\t\tdirection += (yChange < 0) ? \"up\" : \"down\";\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn direction;\n\t\t};\n\n\t\tthis.applyBounds = (newBounds, sticky) => {\n\t\t\tlet x, y, forceZeroVelocity, e, parent, isRoot;\n\t\t\tif (newBounds && vars.bounds !== newBounds) {\n\t\t\t\tvars.bounds = newBounds;\n\t\t\t\treturn self.update(true, sticky);\n\t\t\t}\n\t\t\tsyncXY(true);\n\t\t\tcalculateBounds();\n\t\t\tif (hasBounds && !isTweening()) {\n\t\t\t\tx = self.x;\n\t\t\t\ty = self.y;\n\t\t\t\tif (x > maxX) {\n\t\t\t\t\tx = maxX;\n\t\t\t\t} else if (x < minX) {\n\t\t\t\t\tx = minX;\n\t\t\t\t}\n\t\t\t\tif (y > maxY) {\n\t\t\t\t\ty = maxY;\n\t\t\t\t} else if (y < minY) {\n\t\t\t\t\ty = minY;\n\t\t\t\t}\n\t\t\t\tif (self.x !== x || self.y !== y) {\n\t\t\t\t\tforceZeroVelocity = true;\n\t\t\t\t\tself.x = self.endX = x;\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.endRotation = x;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.y = self.endY = y;\n\t\t\t\t\t}\n\t\t\t\t\tdirty = true;\n\t\t\t\t\trender(true);\n\t\t\t\t\tif (self.autoScroll && !self.isDragging) {\n\t\t\t\t\t\t_recordMaxScrolls(target.parentNode);\n\t\t\t\t\t\te = target;\n\t\t\t\t\t\t_windowProxy.scrollTop = ((_win.pageYOffset != null) ? _win.pageYOffset : (ownerDoc.documentElement.scrollTop != null) ? ownerDoc.documentElement.scrollTop : ownerDoc.body.scrollTop);\n\t\t\t\t\t\t_windowProxy.scrollLeft = ((_win.pageXOffset != null) ? _win.pageXOffset : (ownerDoc.documentElement.scrollLeft != null) ? ownerDoc.documentElement.scrollLeft : ownerDoc.body.scrollLeft);\n\t\t\t\t\t\twhile (e && !isRoot) { //walk up the chain and sense wherever the scrollTop/scrollLeft exceeds the maximum.\n\t\t\t\t\t\t\tisRoot = _isRoot(e.parentNode);\n\t\t\t\t\t\t\tparent = isRoot ? _windowProxy : e.parentNode;\n\t\t\t\t\t\t\tif (allowY && parent.scrollTop > parent._gsMaxScrollY) {\n\t\t\t\t\t\t\t\tparent.scrollTop = parent._gsMaxScrollY;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowX && parent.scrollLeft > parent._gsMaxScrollX) {\n\t\t\t\t\t\t\t\tparent.scrollLeft = parent._gsMaxScrollX;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\te = parent;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (self.isThrowing && (forceZeroVelocity || self.endX > maxX || self.endX < minX || self.endY > maxY || self.endY < minY)) {\n\t\t\t\t\tanimate(vars.inertia || vars.throwProps, forceZeroVelocity);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.update = (applyBounds, sticky, ignoreExternalChanges) => {\n\t\t\tif (sticky && self.isPressed) { // in case the element was repositioned in the document flow, thus its x/y may be identical but its position is actually quite different.\n\t\t\t\tlet m = getGlobalMatrix(target),\n\t\t\t\t\tp = innerMatrix.apply({x: self.x - startElementX, y: self.y - startElementY}),\n\t\t\t\t\tm2 = getGlobalMatrix(target.parentNode, true);\n\t\t\t\tm2.apply({x: m.e - p.x, y: m.f - p.y}, p);\n\t\t\t\tself.x -= p.x - m2.e;\n\t\t\t\tself.y -= p.y - m2.f;\n\t\t\t\trender(true);\n\t\t\t\trecordStartPositions();\n\t\t\t}\n\t\t\tlet { x, y } = self;\n\t\t\tupdateMatrix(!sticky);\n\t\t\tif (applyBounds) {\n\t\t\t\tself.applyBounds();\n\t\t\t} else {\n\t\t\t\tdirty && ignoreExternalChanges && render(true);\n\t\t\t\tsyncXY(true);\n\t\t\t}\n\t\t\tif (sticky) {\n\t\t\t\tsetPointerPosition(self.pointerX, self.pointerY);\n\t\t\t\tdirty && render(true);\n\t\t\t}\n\t\t\tif (self.isPressed && !sticky && ((allowX && Math.abs(x - self.x) > 0.01) || (allowY && (Math.abs(y - self.y) > 0.01 && !rotationMode)))) {\n\t\t\t\trecordStartPositions();\n\t\t\t}\n\t\t\tif (self.autoScroll) {\n\t\t\t\t_recordMaxScrolls(target.parentNode, self.isDragging);\n\t\t\t\tcheckAutoScrollBounds = self.isDragging;\n\t\t\t\trender(true);\n\t\t\t\t//in case reparenting occurred.\n\t\t\t\t_removeScrollListener(target, updateScroll);\n\t\t\t\t_addScrollListener(target, updateScroll);\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.enable = type => {\n\t\t\tlet setVars = {lazy: true},\n\t\t\t\tid, i, trigger;\n\t\t\tif (vars.cursor !== false) {\n\t\t\t\tsetVars.cursor = vars.cursor || _defaultCursor;\n\t\t\t}\n\t\t\tif (gsap.utils.checkPrefix(\"touchCallout\")) {\n\t\t\t\tsetVars.touchCallout = \"none\";\n\t\t\t}\n\t\t\tif (type !== \"soft\") {\n\t\t\t\t_setTouchActionForAllDescendants(triggers, (allowX === allowY) ? \"none\" : (vars.allowNativeTouchScrolling && (target.scrollHeight === target.clientHeight) === (target.scrollWidth === target.clientHeight)) || vars.allowEventDefault ? \"manipulation\" : allowX ? \"pan-y\" : \"pan-x\"); // Some browsers like Internet Explorer will fire a pointercancel event when the user attempts to drag when touchAction is \"manipulate\" because it's perceived as a pan. If the element has scrollable content in only one direction, we should use pan-x or pan-y accordingly so that the pointercancel doesn't prevent dragging.\n\t\t\t\ti = triggers.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\ttrigger = triggers[i];\n\t\t\t\t\t_supportsPointer || _addListener(trigger, \"mousedown\", onPress);\n\t\t\t\t\t_addListener(trigger, \"touchstart\", onPress);\n\t\t\t\t\t_addListener(trigger, \"click\", onClick, true); // note: used to pass true for capture but it prevented click-to-play-video functionality in Firefox.\n\t\t\t\t\tgsap.set(trigger, setVars);\n\t\t\t\t\tif (trigger.getBBox && trigger.ownerSVGElement && allowX !== allowY) { // a bug in chrome doesn't respect touch-action on SVG elements - it only works if we set it on the parent SVG.\n\t\t\t\t\t\tgsap.set(trigger.ownerSVGElement, {touchAction: vars.allowNativeTouchScrolling || vars.allowEventDefault ? \"manipulation\" : allowX ? \"pan-y\" : \"pan-x\"});\n\t\t\t\t\t}\n\t\t\t\t\tvars.allowContextMenu || _addListener(trigger, \"contextmenu\", onContextMenu);\n\t\t\t\t}\n\t\t\t\t_setSelectable(triggers, false);\n\t\t\t}\n\t\t\t_addScrollListener(target, updateScroll);\n\t\t\tenabled = true;\n\t\t\tif (InertiaPlugin && type !== \"soft\") {\n\t\t\t\tInertiaPlugin.track(scrollProxy || target, (xyMode ? \"x,y\" : rotationMode ? \"rotation\" : \"top,left\"));\n\t\t\t}\n\t\t\ttarget._gsDragID = id = target._gsDragID || (\"d\" + (_lookupCount++));\n\t\t\t_lookup[id] = self;\n\t\t\tif (scrollProxy) {\n\t\t\t\tscrollProxy.enable();\n\t\t\t\tscrollProxy.element._gsDragID = id;\n\t\t\t}\n\t\t\t(vars.bounds || rotationMode) && recordStartPositions();\n\t\t\tvars.bounds && self.applyBounds();\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.disable = type => {\n\t\t\tlet dragging = self.isDragging,\n\t\t\t\ti = triggers.length,\n\t\t\t\ttrigger;\n\t\t\twhile (--i > -1) {\n\t\t\t\t_setStyle(triggers[i], \"cursor\", null);\n\t\t\t}\n\t\t\tif (type !== \"soft\") {\n\t\t\t\t_setTouchActionForAllDescendants(triggers, null);\n\t\t\t\ti = triggers.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\ttrigger = triggers[i];\n\t\t\t\t\t_setStyle(trigger, \"touchCallout\", null);\n\t\t\t\t\t_removeListener(trigger, \"mousedown\", onPress);\n\t\t\t\t\t_removeListener(trigger, \"touchstart\", onPress);\n\t\t\t\t\t_removeListener(trigger, \"click\", onClick, true);\n\t\t\t\t\t_removeListener(trigger, \"contextmenu\", onContextMenu);\n\t\t\t\t}\n\t\t\t\t_setSelectable(triggers, true);\n\t\t\t\tif (touchEventTarget) {\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchcancel\", onRelease);\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchend\", onRelease);\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchmove\", onMove);\n\t\t\t\t}\n\t\t\t\t_removeListener(ownerDoc, \"mouseup\", onRelease);\n\t\t\t\t_removeListener(ownerDoc, \"mousemove\", onMove);\n\t\t\t}\n\t\t\t_removeScrollListener(target, updateScroll);\n\t\t\tenabled = false;\n\t\t\tif (InertiaPlugin && type !== \"soft\") {\n\t\t\t\tInertiaPlugin.untrack(scrollProxy || target, (xyMode ? \"x,y\" : rotationMode ? \"rotation\" : \"top,left\"));\n\t\t\t\tself.tween && self.tween.kill();\n\t\t\t}\n\t\t\tscrollProxy && scrollProxy.disable();\n\t\t\t_removeFromRenderQueue(render);\n\t\t\tself.isDragging = self.isPressed = isClicking = false;\n\t\t\tdragging && _dispatchEvent(self, \"dragend\", \"onDragEnd\");\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.enabled = function(value, type) {\n\t\t\treturn arguments.length ? (value ? self.enable(type) : self.disable(type)) : enabled;\n\t\t};\n\n\t\tthis.kill = function() {\n\t\t\tself.isThrowing = false;\n\t\t\tself.tween && self.tween.kill();\n\t\t\tself.disable();\n\t\t\tgsap.set(triggers, {clearProps:\"userSelect\"});\n\t\t\tdelete _lookup[target._gsDragID];\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.revert = function() {\n\t\t\tthis.kill();\n\t\t\tthis.styles && this.styles.revert();\n\t\t};\n\n\t\tif (~type.indexOf(\"scroll\")) {\n\t\t\tscrollProxy = this.scrollProxy = new ScrollProxy(target, _extend({onKill:function() { //ScrollProxy's onKill() gets called if/when the ScrollProxy senses that the user interacted with the scroll position manually (like using the scrollbar). IE9 doesn't fire the \"mouseup\" properly when users drag the scrollbar of an element, so this works around that issue.\n\t\t\t\t\tself.isPressed && onRelease(null);\n\t\t\t}}, vars));\n\t\t\t//a bug in many Android devices' stock browser causes scrollTop to get forced back to 0 after it is altered via JS, so we set overflow to \"hidden\" on mobile/touch devices (they hide the scroll bar anyway). That works around the bug. (This bug is discussed at https://code.google.com/p/android/issues/detail?id=19625)\n\t\t\ttarget.style.overflowY = (allowY && !_isTouchDevice) ? \"auto\" : \"hidden\";\n\t\t\ttarget.style.overflowX = (allowX && !_isTouchDevice) ? \"auto\" : \"hidden\";\n\t\t\ttarget = scrollProxy.content;\n\t\t}\n\n\t\tif (rotationMode) {\n\t\t\tkillProps.rotation = 1;\n\t\t} else {\n\t\t\tif (allowX) {\n\t\t\t\tkillProps[xProp] = 1;\n\t\t\t}\n\t\t\tif (allowY) {\n\t\t\t\tkillProps[yProp] = 1;\n\t\t\t}\n\t\t}\n\n\t\tgsCache.force3D = (\"force3D\" in vars) ? vars.force3D : true; //otherwise, normal dragging would be in 2D and then as soon as it's released and there's an inertia tween, it'd jump to 3D which can create an initial jump due to the work the browser must to do layerize it.\n\n\t\t_context(this);\n\t\tthis.enable();\n\t}\n\n\n\n\n\tstatic register(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t}\n\n\tstatic create(targets, vars) {\n\t\t_coreInitted || _initCore(true);\n\t\treturn _toArray(targets).map(target => new Draggable(target, vars));\n\t}\n\n\tstatic get(target) {\n\t\treturn _lookup[(_toArray(target)[0] || {})._gsDragID];\n\t}\n\n\tstatic timeSinceDrag() {\n\t\treturn (_getTime() - _lastDragTime) / 1000;\n\t}\n\n\tstatic hitTest(obj1, obj2, threshold) {\n\t\tif (obj1 === obj2) {\n\t\t\treturn false;\n\t\t}\n\t\tlet r1 = _parseRect(obj1),\n\t\t\tr2 = _parseRect(obj2),\n\t\t\t{ top, left, right, bottom, width, height } = r1,\n\t\t\tisOutside = (r2.left > right || r2.right < left || r2.top > bottom || r2.bottom < top),\n\t\t\toverlap, area, isRatio;\n\t\tif (isOutside || !threshold) {\n\t\t\treturn !isOutside;\n\t\t}\n\t\tisRatio = ((threshold + \"\").indexOf(\"%\") !== -1);\n\t\tthreshold = parseFloat(threshold) || 0;\n\t\toverlap = {left: Math.max(left, r2.left), top: Math.max(top, r2.top)};\n\t\toverlap.width = Math.min(right, r2.right) - overlap.left;\n\t\toverlap.height = Math.min(bottom, r2.bottom) - overlap.top;\n\t\tif (overlap.width < 0 || overlap.height < 0) {\n\t\t\treturn false;\n\t\t}\n\t\tif (isRatio) {\n\t\t\tthreshold *= 0.01;\n\t\t\tarea = overlap.width * overlap.height;\n\t\t\treturn (area >= width * height * threshold || area >= r2.width * r2.height * threshold);\n\t\t}\n\t\treturn (overlap.width > threshold && overlap.height > threshold);\n\t}\n\n}\n\n_setDefaults(Draggable.prototype, {pointerX:0, pointerY: 0, startX: 0, startY: 0, deltaX: 0, deltaY: 0, isDragging: false, isPressed: false});\n\nDraggable.zIndex = 1000;\nDraggable.version = \"3.13.0\";\n\n_getGSAP() && gsap.registerPlugin(Draggable);\n\nexport { Draggable as default };"], "names": ["_createSibling", "element", "i", "parentNode", "_doc", "_setDoc", "svg", "_svgOwner", "ns", "getAttribute", "type", "x", "y", "css", "e", "createElementNS", "replace", "createElement", "_svgContainer", "setAttribute", "append<PERSON><PERSON><PERSON>", "_divContainer", "style", "cssText", "_setMatrix", "m", "a", "b", "c", "d", "f", "_win", "_doc<PERSON>lement", "_body", "_identityMatrix", "_gEl", "_hasOffsetBug", "_transformProp", "_transformOriginProp", "doc", "ownerDocument", "window", "Matrix2D", "documentElement", "body", "transform", "d1", "d2", "root", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetParent", "<PERSON><PERSON><PERSON><PERSON>", "_forceNonZeroScale", "cache", "_gsap", "uncache", "get", "scaleX", "scaleY", "renderTransform", "push", "_svgTemps", "_divTemps", "_getDocScrollTop", "pageYOffset", "scrollTop", "_getDocScrollLeft", "pageXOffset", "scrollLeft", "ownerSVGElement", "tagName", "toLowerCase", "_isFixed", "getComputedStyle", "position", "nodeType", "_placeSiblings", "adjustGOffset", "container", "cs", "isRootSVG", "siblings", "parent", "appendToEl", "shadowRoot", "length", "_getCTM", "getCTM", "removeProperty", "clone", "getBBox", "baseVal", "numberOfItems", "_consolidate", "multiply", "getItem", "matrix", "offsetLeft", "offsetTop", "top", "left", "inverse", "this", "determinant", "a2", "b2", "c2", "e2", "f2", "equals", "apply", "point", "decoratee", "getGlobalMatrix", "includeScrollInFixed", "zeroScales", "temps", "b1", "getBoundingClientRect", "b3", "isFixed", "_windowExists", "_getGSAP", "gsap", "registerPlugin", "_isFunction", "value", "_isObject", "_isUndefined", "_emptyFunc", "_round", "Math", "round", "_createElement", "_copy", "obj", "factor", "p", "copy", "_setTouchActionForAllDescendants", "elements", "children", "touchAction", "_renderQueueTick", "_renderQueue", "for<PERSON>ach", "func", "_renderQueueTimeout", "ticker", "remove", "_removeFromRenderQueue", "splice", "to", "overwrite", "delay", "duration", "onComplete", "data", "_addListener", "capture", "addEventListener", "touchType", "_touchEventLookup", "_supportsPassive", "passive", "_removeListener", "removeEventListener", "_preventDefault", "event", "preventDefault", "preventManipulation", "_onMultiTouchDocumentEnd", "_isMultiTouching", "touches", "_dragCount", "target", "_onMultiTouchDocument", "_addScrollListener", "callback", "_isRoot", "_removeScrollListener", "_getMaxScroll", "axis", "dim", "scroll", "client", "max", "_recordMaxScrolls", "<PERSON><PERSON><PERSON><PERSON>", "_windowProxy", "_gsMaxScrollX", "_gsMaxScrollY", "_gsScrollX", "_gsScrollY", "_setStyle", "property", "_checkPrefix", "_getComputedStyle", "Element", "host", "_parseRect", "_tempRect", "width", "right", "clientWidth", "innerWidth", "height", "bottom", "innerHeight", "clientHeight", "r", "pageX", "_toArray", "pageY", "_dispatchEvent", "callback<PERSON><PERSON>", "result", "vars", "listeners", "_listeners", "callbackScope", "pointerEvent", "dispatchEvent", "_getBounds", "context", "offset", "_getElementBounds", "min", "minX", "minRotation", "minY", "maxX", "maxRotation", "maxY", "_parseInertia", "draggable", "snap", "forceZeroVelocity", "l", "Array", "end", "call", "velocity", "_isClickable", "_clickableTagExp", "test", "nodeName", "_setSelectable", "selectable", "ondragstart", "onselectstart", "set", "lazy", "userSelect", "ScrollProxy", "utils", "toArray", "elementWidth", "elementHeight", "contentHeight", "nextNode", "transformStart", "transformEnd", "content", "document", "node", "<PERSON><PERSON><PERSON><PERSON>", "prevTop", "prevLeft", "scrollWidth", "scrollHeight", "extraPadRight", "maxLeft", "maxTop", "_supports3D", "force3D", "force", "arguments", "dif", "oldOffset", "killTweensOf", "onKill", "_skip", "paddingRight", "maxScrollTop", "maxScrollLeft", "disable", "nextS<PERSON>ling", "enable", "calibrate", "widthMatches", "offsetHeight", "display", "parseFloat", "paddingLeft", "_addPaddingBR", "overflow", "verticalAlign", "boxSizing", "paddingBottom", "_initCore", "required", "nav", "navigator", "_tempDiv", "_supportsPointer", "PointerEvent", "_placeholderDiv", "_defaultCursor", "cursor", "_isAndroid", "userAgent", "indexOf", "_isTouchDevice", "MaxTouchPoints", "msMaxTouchPoints", "div", "child", "childStyle", "val", "types", "standard", "split", "converted", "Object", "defineProperty", "_lookup", "isPressed", "endDrag", "_coreInitted", "InertiaPlugin", "plugins", "inertia", "_context", "core", "checkPrefix", "_getStyleSaver", "getStyleSaver", "console", "warn", "_isArray", "isArray", "_RAD2DEG", "PI", "_bigNum", "_getTime", "Date", "now", "getTime", "_lookupCount", "_lastDragTime", "_temp1", "_point1", "p1", "p2", "p3", "p4", "bbox", "isSVG", "viewBox", "borderLeftWidth", "borderRightWidth", "borderTopWidth", "borderBottomWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "list", "_this", "Draggable", "register", "create", "targets", "map", "_gsDragID", "timeSinceDrag", "hitTest", "obj1", "obj2", "threshold", "overlap", "area", "isRatio", "r1", "r2", "isOutside", "styles", "_this2", "rotation", "dragResistance", "edgeResistance", "isNaN", "lockAxis", "autoScroll", "lockedAxis", "allowEventDefault", "getProperty", "getPropAsNum", "unit", "gsCache", "onContextMenu", "stopImmediatePropagation", "render", "suppressEvents", "self", "isDragging", "checkAutoScrollBounds", "dirty", "isRoot", "rect", "pointerX", "pointerY", "changeX", "changeY", "gap", "autoScrollFactor", "ownerDoc", "allowY", "autoScrollMarginBottom", "autoScrollMarginTop", "allowX", "autoScrollMarginRight", "autoScrollMarginLeft", "scrollTo", "setPointerPosition", "rotationMode", "deltaX", "scrollProxy", "deltaY", "xyMode", "hasDragCallback", "isDispatching", "syncXY", "skipOnUpdate", "skipSnap", "snappedValue", "getCache", "snapX", "snapY", "snapXY", "isThrowing", "buildSnapFunc", "n", "edgeTolerance", "closest", "absDif", "calculateBounds", "bounds", "targetBounds", "snapIsRaw", "hasBounds", "xProp", "yProp", "liveSnap", "points", "buildPointSnapFunc", "radius", "dx", "dy", "dist", "minDist", "onThrowComplete", "onThrowInterrupt", "animate", "tween", "overshootTolerance", "resistance", "throwResistance", "linkedProps", "minDuration", "maxDuration", "overshoot", "inherit", "onInterrupt", "onUpdate", "fastMode", "onUpdateParams", "endX", "endY", "endRotation", "play", "applyBounds", "updateMatrix", "shiftStart", "start", "startPointerX", "startPointerY", "recordStartPositions", "parsed<PERSON><PERSON>in", "offsetX", "offsetY", "innerMatrix", "startElementY", "startElementX", "isTweening", "xOrigin", "y<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "atan2", "startX", "startY", "removePlaceholder", "onPress", "enabled", "clickTime", "isPreventingDefault", "interrupted", "dragged", "touchEventTarget", "currentTarget", "onRelease", "onMove", "touchDragAxis", "isClicking", "isClickable", "dragClickables", "triggers", "allowNativeTouchScrolling", "allowContextMenu", "ctrl<PERSON>ey", "which", "changedTouches", "touch", "touchID", "identifier", "pointerId", "_addToRenderQueue", "add", "kill", "killProps", "zIndexBoost", "zIndex", "onDrag", "drag", "hasMoveCallback", "move", "activeCursor", "updateScroll", "onClick", "time", "recentlyClicked", "recentlyDragged", "dragEndTime", "alreadyDispatched", "clickDispatch", "defaultPrevented", "alreadyDispatchedTrusted", "trustedClickDispatch", "trusted", "isTrusted", "suppressClickOnDrag", "detail", "localizePoint", "old", "minimumMovement", "trigger", "handle", "clickableTest", "isActive", "originalEvent", "abs", "lockAxisOnTouchScroll", "onLockAxis", "cancelable", "invokeOnMove", "xChange", "yChange", "temp", "dragTolerance", "prevPointerX", "prevPointerY", "prevStartElementY", "prevX", "prevY", "prevEndX", "prevEndY", "prevEndRotation", "prevDirty", "_hasTouchID", "ID", "syntheticEvent", "eventTarget", "syntheticClick", "wasDragging", "isContextMenuRelease", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "delayedCall", "throwProps", "click", "createEvent", "initMouseEvent", "screenX", "screenY", "startDrag", "align", "timeSinceClick", "getDirection", "from", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratio", "direction", "mode", "getVelocity", "newBounds", "sticky", "update", "ignoreExternalChanges", "m2", "id", "setVars", "touchCallout", "track", "dragging", "untrack", "clearProps", "revert", "_extend", "defaults", "overflowY", "overflowX", "_setDefaults", "prototype", "version"], "mappings": ";;;;;;;;;6VA0FkB,SAAjBA,EAAkBC,EAASC,MACtBD,EAAQE,aAAeC,GAAQC,EAAQJ,IAAW,KACjDK,EAAMC,EAAUN,GACnBO,EAAKF,EAAOA,EAAIG,aAAa,UAAY,6BAAgC,+BACzEC,EAAOJ,EAAOJ,EAAI,OAAS,IAAO,MAClCS,EAAU,IAANT,EAAU,EAAI,IAClBU,EAAU,IAANV,EAAU,IAAM,EACpBW,EAAM,0EACNC,EAAIV,EAAKW,gBAAkBX,EAAKW,gBAAgBP,EAAGQ,QAAQ,SAAU,QAASN,GAAQN,EAAKa,cAAcP,UACtGR,IACEI,GAScY,EAAlBA,GAAkClB,EAAeC,GACjDa,EAAEK,aAAa,QAAS,KACxBL,EAAEK,aAAa,SAAU,KACzBL,EAAEK,aAAa,YAAa,aAAeR,EAAI,IAAMC,EAAI,KACzDM,EAAcE,YAAYN,KAZrBO,KACJA,EAAgBrB,EAAeC,IACjBqB,MAAMC,QAAUV,GAE/BC,EAAEQ,MAAMC,QAAUV,EAAM,gCAAkCD,EAAI,WAAaD,EAAI,KAC/EU,EAAcD,YAAYN,KAUrBA,OAEF,4BA6FM,SAAbU,EAAcC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGf,EAAGgB,UAC/BL,EAAEC,EAAIA,EACND,EAAEE,EAAIA,EACNF,EAAEG,EAAIA,EACNH,EAAEI,EAAIA,EACNJ,EAAEX,EAAIA,EACNW,EAAEK,EAAIA,EACCL,EAhNT,IAAIrB,EAAM2B,EAAMC,EAAaC,EAAOZ,EAAeH,EAAegB,EAAiBC,EAGlFC,IAFAC,EAAiB,YACjBC,EAAuBD,EAAiB,SAExChC,EAAU,SAAVA,QAAUJ,OACLsC,EAAMtC,EAAQuC,eAAiBvC,IAC7BoC,KAAkBpC,EAAQqB,QAAU,gBAAiBrB,EAAQqB,QAElEgB,GADAD,EAAiB,eACuB,eAElCE,EAAIpC,aAAeoC,EAAMA,EAAIpC,iBACpC4B,EAAOU,OACPP,EAAkB,IAAIQ,GAClBH,EAAK,CAERP,GADA5B,EAAOmC,GACWI,gBAClBV,EAAQM,EAAIK,MACZT,EAAO/B,EAAKW,gBAAgB,6BAA8B,MAErDO,MAAMuB,UAAY,WAEnBC,EAAKP,EAAItB,cAAc,OAC1B8B,EAAKR,EAAItB,cAAc,OACvB+B,EAAOT,IAAQA,EAAIK,MAAQL,EAAIU,mBAC5BD,GAAQA,EAAK5B,cAChB4B,EAAK5B,YAAY0B,GACjBA,EAAG1B,YAAY2B,GACfD,EAAG3B,aAAa,QAAS,kDACzBiB,EAAiBW,EAAGG,eAAiBJ,EACrCE,EAAKG,YAAYL,WAGZP,GAERa,EAAqB,SAArBA,mBAAqBtC,WAChBY,EAAG2B,EACAvC,GAAKA,IAAMmB,IACjBoB,EAAQvC,EAAEwC,QACDD,EAAME,SAAWF,EAAMG,IAAI1C,EAAG,KACnCuC,IAAUA,EAAMI,SAAWJ,EAAMK,QAAUL,EAAMM,kBACpDN,EAAMI,OAASJ,EAAMK,OAAS,KAC9BL,EAAMM,gBAAgB,EAAGN,GACzB3B,EAAIA,EAAEkC,KAAKP,GAAU3B,EAAI,CAAC2B,IAE3BvC,EAAIA,EAAEX,kBAEAuB,GAoBRmC,EAAY,GACZC,EAAY,GACZC,EAAmB,SAAnBA,0BAAyBhC,EAAKiC,aAAgB5D,EAAK6D,WAAajC,EAAYiC,WAAahC,EAAMgC,WAAa,GAC5GC,EAAoB,SAApBA,2BAA0BnC,EAAKoC,aAAe/D,EAAKgE,YAAcpC,EAAYoC,YAAcnC,EAAMmC,YAAc,GAC/G7D,EAAY,SAAZA,UAAYN,UAAWA,EAAQoE,kBAA6D,SAAxCpE,EAAQqE,QAAU,IAAIC,cAA0BtE,EAAU,OAC9GuE,EAAW,SAAXA,SAAWvE,SACsC,UAA5C8B,EAAK0C,iBAAiBxE,GAASyE,YAGnCzE,EAAUA,EAAQE,aACkB,IAArBF,EAAQ0E,SACfH,SAASvE,YAsDlB2E,EAAiB,SAAjBA,eAAkB3E,EAAS4E,OAMzBC,EAAWrD,EAAGE,EAAGhB,EAAGC,EAAGmE,EALpBzE,EAAMC,EAAUN,GACnB+E,EAAY/E,IAAYK,EACxB2E,EAAW3E,EAAMuD,EAAYC,EAC7BoB,EAASjF,EAAQE,WACjBgF,EAAaD,IAAW5E,GAAO4E,EAAOE,YAAcF,EAAOE,WAAWhE,YAAc8D,EAAOE,WAAaF,KAErGjF,IAAY8B,SACR9B,KAERgF,EAASI,QAAUJ,EAASrB,KAAK5D,EAAeC,EAAS,GAAID,EAAeC,EAAS,GAAID,EAAeC,EAAS,IACjH6E,EAAYxE,EAAMY,EAAgBG,EAC9Bf,EACC0E,GAEHrE,IADAgB,EA3BO,SAAV2D,QAAUhF,OAERuC,EADGpB,EAAInB,EAAIiF,gBAEP9D,IACJoB,EAAYvC,EAAIgB,MAAMe,GACtB/B,EAAIgB,MAAMe,GAAkB,OAC5B/B,EAAIc,YAAYe,GAChBV,EAAIU,EAAKoD,SACTjF,EAAI6C,YAAYhB,GAChBU,EAAavC,EAAIgB,MAAMe,GAAkBQ,EAAavC,EAAIgB,MAAMkE,eAAenD,EAAerB,QAAQ,WAAY,OAAOuD,gBAEnH9C,GAAKS,EAAgBuD,QAgBtBH,CAAQrF,IACLa,EAAIa,EAAED,EACbd,GAAKe,EAAEG,EAAIH,EAAEE,EACbJ,EAAIS,GACMjC,EAAQyF,SAClB/D,EAAI1B,EAAQyF,UAGZ/E,GADAc,GADAA,EAAIxB,EAAQ4C,UAAY5C,EAAQ4C,UAAU8C,QAAU,IAC7CC,cAAoD,EAAlBnE,EAAEmE,cA1C/B,SAAfC,aAAepE,WACVG,EAAI,IAAIc,GACXxC,EAAI,EACEA,EAAIuB,EAAEmE,cAAe1F,IAC3B0B,EAAEkE,SAASrE,EAAEsE,QAAQ7F,GAAG8F,eAElBpE,EAoC0DiE,CAAapE,GAAKA,EAAEsE,QAAQ,GAAGC,OAAvE9D,GACjBR,EAAIC,EAAEhB,EAAIc,EAAEG,EAAID,EAAEf,EACxBA,EAAIa,EAAEE,EAAIA,EAAEhB,EAAIc,EAAEI,EAAIF,EAAEf,IAExBa,EAAI,IAAIiB,GACR/B,EAAIC,EAAI,GAELiE,GAAmD,MAAlC5E,EAAQqE,QAAQC,gBACpC5D,EAAIC,EAAI,IAERoE,EAAY1E,EAAM4E,GAAQ9D,YAAY0D,GACvCA,EAAU3D,aAAa,YAAa,UAAYM,EAAEC,EAAI,IAAMD,EAAEE,EAAI,IAAMF,EAAEG,EAAI,IAAMH,EAAEI,EAAI,KAAOJ,EAAEX,EAAIH,GAAK,KAAOc,EAAEK,EAAIlB,GAAK,SACxH,IACND,EAAIC,EAAI,EACJwB,MACHX,EAAIxB,EAAQiD,aACZvB,EAAI1B,GACS0B,EAANA,GAAUA,EAAExB,aAAewB,IAAMF,GAAKE,EAAExB,YACe,GAAxD4B,EAAK0C,iBAAiB9C,GAAGU,GAAkB,IAAIgD,SACnD1E,EAAIgB,EAAEsE,WACNrF,EAAIe,EAAEuE,UACNvE,EAAI,MAKa,cADpBoD,EAAKhD,EAAK0C,iBAAiBxE,IACpByE,UAA2C,UAAhBK,EAAGL,aACpCjD,EAAIxB,EAAQiD,aACLgC,GAAUA,IAAWzD,GAC3Bd,GAAKuE,EAAOd,YAAc,EAC1BxD,GAAKsE,EAAOjB,WAAa,EACzBiB,EAASA,EAAO/E,YAGlBwB,EAAImD,EAAUxD,OACZ6E,IAAOlG,EAAQiG,UAAYtF,EAAK,KAClCe,EAAEyE,KAAQnG,EAAQgG,WAAatF,EAAK,KACpCgB,EAAEU,GAAkB0C,EAAG1C,GACvBV,EAAEW,GAAwByC,EAAGzC,GAM7BX,EAAE+C,SAA2B,UAAhBK,EAAGL,SAAuB,QAAU,WACjDS,EAAW/D,YAAY0D,UAEjBA,GAYIpC,2BAKZ2D,QAAA,uBACM3E,EAAoB4E,KAApB5E,EAAGC,EAAiB2E,KAAjB3E,EAAGC,EAAc0E,KAAd1E,EAAGC,EAAWyE,KAAXzE,EAAGf,EAAQwF,KAARxF,EAAGgB,EAAKwE,KAALxE,EACnByE,EAAe7E,EAAIG,EAAIF,EAAIC,GAAM,aAC3BJ,EACN8E,KACAzE,EAAI0E,GACH5E,EAAI4E,GACJ3E,EAAI2E,EACL7E,EAAI6E,GACH3E,EAAIE,EAAID,EAAIf,GAAKyF,IAChB7E,EAAII,EAAIH,EAAIb,GAAKyF,MAIrBT,SAAA,kBAASE,OACHtE,EAAoB4E,KAApB5E,EAAGC,EAAiB2E,KAAjB3E,EAAGC,EAAc0E,KAAd1E,EAAGC,EAAWyE,KAAXzE,EAAGf,EAAQwF,KAARxF,EAAGgB,EAAKwE,KAALxE,EACnB0E,EAAKR,EAAOtE,EACZ+E,EAAKT,EAAOpE,EACZ8E,EAAKV,EAAOrE,EACZoB,EAAKiD,EAAOnE,EACZ8E,EAAKX,EAAOlF,EACZ8F,EAAKZ,EAAOlE,SACNN,EAAW8E,KACjBE,EAAK9E,EAAIgF,EAAK9E,EACd4E,EAAK7E,EAAI+E,EAAK7E,EACd4E,EAAK/E,EAAIqB,EAAKnB,EACd6E,EAAK9E,EAAIoB,EAAKlB,EACdf,EAAI6F,EAAKjF,EAAIkF,EAAKhF,EAClBE,EAAI6E,EAAKhF,EAAIiF,EAAK/E,MAGpB4D,MAAA,wBACQ,IAAI/C,SAAS4D,KAAK5E,EAAG4E,KAAK3E,EAAG2E,KAAK1E,EAAG0E,KAAKzE,EAAGyE,KAAKxF,EAAGwF,KAAKxE,MAGlE+E,OAAA,gBAAOb,OACDtE,EAAoB4E,KAApB5E,EAAGC,EAAiB2E,KAAjB3E,EAAGC,EAAc0E,KAAd1E,EAAGC,EAAWyE,KAAXzE,EAAGf,EAAQwF,KAARxF,EAAGgB,EAAKwE,KAALxE,SACZJ,IAAMsE,EAAOtE,GAAKC,IAAMqE,EAAOrE,GAAKC,IAAMoE,EAAOpE,GAAKC,IAAMmE,EAAOnE,GAAKf,IAAMkF,EAAOlF,GAAKgB,IAAMkE,EAAOlE,KAGhHgF,MAAA,eAAMC,EAAOC,YAAAA,IAAAA,EAAU,QACjBrG,EAAQoG,EAARpG,EAAGC,EAAKmG,EAALnG,EACNc,EAAoB4E,KAApB5E,EAAGC,EAAiB2E,KAAjB3E,EAAGC,EAAc0E,KAAd1E,EAAGC,EAAWyE,KAAXzE,EAAGf,EAAQwF,KAARxF,EAAGgB,EAAKwE,KAALxE,SACjBkF,EAAUrG,EAAKA,EAAIe,EAAId,EAAIgB,EAAId,GAAM,EACrCkG,EAAUpG,EAAKD,EAAIgB,EAAIf,EAAIiB,EAAIC,GAAM,EAC9BkF,+BAjDItF,EAAKC,EAAKC,EAAKC,EAAKf,EAAKgB,YAAzBJ,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGf,IAAAA,EAAE,YAAGgB,IAAAA,EAAE,GACtCN,EAAW8E,KAAM5E,EAAGC,EAAGC,EAAGC,EAAGf,EAAGgB,GA4D3B,SAASmF,gBAAgBhH,EAASoG,EAASxB,EAAeqC,OAC3DjH,IAAYA,EAAQE,aAAeC,GAAQC,EAAQJ,IAAU0C,kBAAoB1C,SAC9E,IAAIyC,OAERyE,EAAa/D,EAAmBnD,GAEnCmH,EADM7G,EAAUN,GACF4D,EAAYC,EAC1BgB,EAAYF,EAAe3E,EAAS4E,GACpCwC,EAAKD,EAAM,GAAGE,wBACdb,EAAKW,EAAM,GAAGE,wBACdC,EAAKH,EAAM,GAAGE,wBACdpC,EAASJ,EAAU3E,WACnBqH,GAAWN,GAAwB1C,EAASvE,GAC5CwB,EAAI,IAAIiB,IACN+D,EAAGL,KAAOiB,EAAGjB,MAAQ,KACrBK,EAAGN,IAAMkB,EAAGlB,KAAO,KACnBoB,EAAGnB,KAAOiB,EAAGjB,MAAQ,KACrBmB,EAAGpB,IAAMkB,EAAGlB,KAAO,IACpBkB,EAAGjB,MAAQoB,EAAU,EAAItD,KACzBmD,EAAGlB,KAAOqB,EAAU,EAAIzD,SAE1BmB,EAAO/B,YAAY2B,GACfqC,MACHE,EAAKF,EAAW9B,OACTgC,MACNZ,EAAKU,EAAWE,IACb5D,OAASgD,EAAG/C,OAAS,EACxB+C,EAAG9C,gBAAgB,EAAG8C,UAGjBJ,EAAU5E,EAAE4E,UAAY5E,EC3Sf,SAAhBgG,UAAyC,oBAAZhF,OAClB,SAAXiF,WAAiBC,IAASF,MAAoBE,GAAOlF,OAAOkF,OAASA,GAAKC,gBAAkBD,GAC9E,SAAdE,EAAcC,SAA2B,mBAAXA,EAClB,SAAZC,EAAYD,SAA2B,iBAAXA,EACb,SAAfE,EAAeF,eAA2B,IAAXA,EAClB,SAAbG,YAAmB,EAGV,SAATC,GAASJ,UAASK,KAAKC,MAAc,IAARN,GAAiB,IAE7B,SAAjBO,GAAkB3H,EAAMF,OACnBM,EAAIV,GAAKW,gBAAkBX,GAAKW,iBAAiBP,GAAM,gCAAgCQ,QAAQ,SAAU,QAASN,GAAQN,GAAKa,cAAcP,UAC1II,EAAEQ,MAAQR,EAAIV,GAAKa,cAAcP,GAajC,SAAR4H,GAASC,EAAKC,OACEC,EAAXC,EAAO,OACND,KAAKF,EACTG,EAAKD,GAAKD,EAASD,EAAIE,GAAKD,EAASD,EAAIE,UAEnCC,EAU2B,SAAnCC,GAAoCC,EAAUd,WAE5Ce,EADG3I,EAAI0I,EAASvD,OAEVnF,KACN4H,EAASc,EAAS1I,GAAGoB,MAAMwH,YAAchB,EAASc,EAAS1I,GAAGoB,MAAMkE,eAAe,iBACnFqD,EAAWD,EAAS1I,GAAG2I,WACXA,EAASxD,QAAUsD,GAAiCE,EAAUf,GAGzD,SAAnBiB,YAAyBC,GAAaC,QAAQ,SAAAC,UAAQA,MAOhC,SAAtBC,YAA6BH,GAAa3D,QAAUsC,GAAKyB,OAAOC,OAAON,IAC9C,SAAzBO,GAAyBJ,WACpBhJ,EAAI8I,GAAa3D,OACdnF,KACF8I,GAAa9I,KAAOgJ,GACvBF,GAAaO,OAAOrJ,EAAG,GAGzByH,GAAK6B,GAAGL,GAAqB,CAACM,WAAU,EAAMC,MAAM,GAAIC,SAAS,EAAGC,WAAWT,GAAqBU,KAAK,eAU3F,SAAfC,GAAgB7J,EAASS,EAAMwI,EAAMa,MAChC9J,EAAQ+J,iBAAkB,KACzBC,EAAYC,GAAkBxJ,GAClCqJ,EAAUA,IAAYI,EAAmB,CAACC,SAAS,GAAS,MAC5DnK,EAAQ+J,iBAAiBC,GAAavJ,EAAMwI,EAAMa,GACjDE,GAAavJ,IAASuJ,GAAchK,EAAQ+J,iBAAiBtJ,EAAMwI,EAAMa,IAG1D,SAAlBM,GAAmBpK,EAASS,EAAMwI,EAAMa,MACnC9J,EAAQqK,oBAAqB,KAC5BL,EAAYC,GAAkBxJ,GAClCT,EAAQqK,oBAAoBL,GAAavJ,EAAMwI,EAAMa,GACpDE,GAAavJ,IAASuJ,GAAchK,EAAQqK,oBAAoB5J,EAAMwI,EAAMa,IAG7D,SAAlBQ,GAAkBC,GACjBA,EAAMC,gBAAkBD,EAAMC,iBAC9BD,EAAME,qBAAuBF,EAAME,sBAUT,SAA3BC,GAA2BH,GAC1BI,GAAoBJ,EAAMK,SAAWC,GAAaN,EAAMK,QAAQxF,OAChEgF,GAAgBG,EAAMO,OAAQ,WAAYJ,IAGnB,SAAxBK,GAAwBR,GACvBI,GAAoBJ,EAAMK,SAAWC,GAAaN,EAAMK,QAAQxF,OAChEyE,GAAaU,EAAMO,OAAQ,WAAYJ,IAErB,SAAnB5G,GAAmBxB,UAAOR,GAAKiC,aAAgBzB,EAAI0B,WAAa1B,EAAII,gBAAgBsB,WAAa1B,EAAIK,KAAKqB,WAAa,EACnG,SAApBC,GAAoB3B,UAAOR,GAAKoC,aAAe5B,EAAI6B,YAAc7B,EAAII,gBAAgByB,YAAc7B,EAAIK,KAAKwB,YAAc,EACrG,SAArB6G,GAAsBnK,EAAGoK,GACxBpB,GAAahJ,EAAG,SAAUoK,GACrBC,GAAQrK,EAAEX,aACd8K,GAAmBnK,EAAEX,WAAY+K,GAGX,SAAxBE,GAAyBtK,EAAGoK,GAC3Bb,GAAgBvJ,EAAG,SAAUoK,GACxBC,GAAQrK,EAAEX,aACdiL,GAAsBtK,EAAEX,WAAY+K,GAItB,SAAhBG,GAAiBpL,EAASqL,OACrBC,EAAgB,MAATD,EAAgB,QAAU,SACpCE,EAAS,SAAWD,EACpBE,EAAS,SAAWF,SACdpD,KAAKuD,IAAI,EAAGP,GAAQlL,GAAWkI,KAAKuD,IAAI1J,GAAYwJ,GAASvJ,EAAMuJ,KAAYzJ,GAAK,QAAUwJ,IAAQvJ,GAAYyJ,IAAWxJ,EAAMwJ,IAAWxL,EAAQuL,GAAUvL,EAAQwL,IAE5J,SAApBE,GAAqB7K,EAAG8K,OACnBjL,EAAI0K,GAAcvK,EAAG,KACxBF,EAAIyK,GAAcvK,EAAG,KAClBqK,GAAQrK,GACXA,EAAI+K,GAEJF,GAAkB7K,EAAEX,WAAYyL,GAEjC9K,EAAEgL,cAAgBnL,EAClBG,EAAEiL,cAAgBnL,EACbgL,IACJ9K,EAAEkL,WAAalL,EAAEsD,YAAc,EAC/BtD,EAAEmL,WAAanL,EAAEmD,WAAa,GAGpB,SAAZiI,GAAajM,EAASkM,EAAUrE,OAC3BxG,EAAQrB,EAAQqB,MACfA,IAGD0G,EAAa1G,EAAM6K,MACtBA,EAAWC,EAAaD,EAAUlM,IAAYkM,GAElC,MAATrE,EACHxG,EAAMkE,gBAAkBlE,EAAMkE,eAAe2G,EAASnL,QAAQ,WAAY,OAAOuD,eAEjFjD,EAAM6K,GAAYrE,GAGA,SAApBuE,GAAoBpM,UAAW8B,GAAK0C,iBAAkBxE,aAAmBqM,QAAWrM,EAAUA,EAAQsM,OAAStM,EAAQE,YAAc,IAAIoM,MAAQtM,GAGpI,SAAbuM,GAAa1L,MACRA,IAAMiB,UACT0K,EAAUrG,KAAOqG,EAAUtG,IAAM,EACjCsG,EAAUC,MAAQD,EAAUE,MAAQ3K,GAAY4K,aAAe9L,EAAE+L,YAAc5K,EAAM2K,aAAe,EACpGH,EAAUK,OAASL,EAAUM,QAAWjM,EAAEkM,aAAe,GAAK,GAAKhL,GAAYiL,aAAgBjL,GAAYiL,aAAenM,EAAEkM,aAAe/K,EAAMgL,cAAgB,EAC1JR,MAEJlK,EAAMzB,EAAE0B,eAAiBpC,GAC5B8M,EAAKlF,EAAalH,EAAEqM,OAAsLrM,EAAE6D,UAAaqD,EAAalH,EAAEsF,OAAU4B,EAAalH,EAAEqF,KAAYiH,GAAStM,GAAG,GAAGwG,wBAAnBxG,EAA5O,CAACsF,KAAMtF,EAAEqM,MAAQjJ,GAAkB3B,GAAM4D,IAAKrF,EAAEuM,MAAQtJ,GAAiBxB,GAAMoK,MAAO7L,EAAEqM,MAAQjJ,GAAkB3B,GAAO,EAAGwK,OAAQjM,EAAEuM,MAAQtJ,GAAiBxB,GAAO,UAChMyF,EAAakF,EAAEP,SAAW3E,EAAakF,EAAER,QAC5CQ,EAAEP,MAAQO,EAAE9G,KAAO8G,EAAER,MACrBQ,EAAEH,OAASG,EAAE/G,IAAM+G,EAAEJ,QACX9E,EAAakF,EAAER,SACzBQ,EAAI,CAACR,MAAOQ,EAAEP,MAAQO,EAAE9G,KAAM0G,OAAQI,EAAEH,OAASG,EAAE/G,IAAKwG,MAAOO,EAAEP,MAAOvG,KAAM8G,EAAE9G,KAAM2G,OAAQG,EAAEH,OAAQ5G,IAAK+G,EAAE/G,MAEzG+G,EAGS,SAAjBI,GAAkBvC,EAAQrK,EAAM6M,OAI9BC,EAHGC,EAAO1C,EAAO0C,KACjBvC,EAAWuC,EAAKF,GAChBG,EAAY3C,EAAO4C,WAAWjN,UAE3BmH,EAAYqD,KACfsC,EAAStC,EAASpE,MAAM2G,EAAKG,eAAiB7C,EAAQ0C,EAAKF,EAAe,WAAa,CAACxC,EAAO8C,gBAE5FH,IAA4C,IAA/B3C,EAAO+C,cAAcpN,KACrC8M,GAAS,GAEHA,EAEK,SAAbO,GAAchD,EAAQiD,OAEpB7H,EAAKC,EAAM6H,EADRnN,EAAIsM,GAASrC,GAAQ,UAEpBjK,EAAE6D,UAAY7D,IAAMiB,GASlBmM,EAAkBpN,EAAGkN,GARtBhG,EAAa+C,EAAO3E,MAMlB,CAACA,KAFRA,EAAO2E,EAAOoD,KAAOpD,EAAOqD,MAAQrD,EAAOsD,aAAe,EAEvClI,IADnBA,EAAM4E,EAAOoD,KAAOpD,EAAOuD,MAAQ,EACP5B,OAAO3B,EAAOW,KAAOX,EAAOwD,MAAQxD,EAAOyD,aAAe,GAAKpI,EAAM0G,QAAQ/B,EAAOW,KAAOX,EAAO0D,MAAQ,GAAKtI,IAL1I8H,EAAS,CAACtN,EAAE,EAAGC,EAAE,GACV,CAACwF,KAAM2E,EAAO3E,KAAO6H,EAAOtN,EAAGwF,IAAK4E,EAAO5E,IAAM8H,EAAOrN,EAAG8L,MAAO3B,EAAO2B,MAAOI,OAAQ/B,EAAO+B,SAwDzF,SAAhB4B,GAAiBC,EAAWC,EAAMlD,EAAKyC,EAAK3F,EAAQqG,OAElDnN,EAAGxB,EAAG4O,EADHrB,EAAO,MAEPmB,KACY,IAAXpG,GAAgBoG,aAAgBG,MAAO,IAC1CtB,EAAKuB,IAAMtN,EAAI,GACfoN,EAAIF,EAAKvJ,OACL0C,EAAU6G,EAAK,QACb1O,EAAI,EAAGA,EAAI4O,EAAG5O,IAClBwB,EAAExB,GAAKoI,GAAMsG,EAAK1O,GAAIsI,YAGlBtI,EAAI,EAAGA,EAAI4O,EAAG5O,IAClBwB,EAAExB,GAAK0O,EAAK1O,GAAKsI,EAGnBkD,GAAO,IACPyC,GAAO,SACGtG,EAAY+G,GACtBnB,EAAKuB,IAAM,SAAAlH,OAETY,EAAMD,EADH+E,EAASoB,EAAKK,KAAKN,EAAW7G,MAEnB,IAAXU,KACCT,EAAUyF,GAAS,KAEjB/E,KADLC,EAAO,GACG8E,EACT9E,EAAKD,GAAK+E,EAAO/E,GAAKD,EAEvBgF,EAAS9E,OAET8E,GAAUhF,SAGLgF,GAGRC,EAAKuB,IAAMJ,SAGTlD,GAAe,IAARA,IACV+B,EAAK/B,IAAMA,IAERyC,GAAe,IAARA,IACVV,EAAKU,IAAMA,GAERU,IACHpB,EAAKyB,SAAW,GAEVzB,EAEO,SAAf0B,GAAelP,OACV4J,WACK5J,IAAYA,EAAQQ,cAAgBR,IAAYgC,OAAsE,UAAnD4H,EAAO5J,EAAQQ,aAAa,qBAA2C,UAAToJ,IAAqBuF,EAAiBC,KAAKpP,EAAQqP,SAAW,KAAmD,SAA5CrP,EAAQQ,aAAa,sBAA0C0O,GAAalP,EAAQE,aAEnR,SAAjBoP,GAAkB3G,EAAU4G,WAE1B1O,EADGZ,EAAI0I,EAASvD,OAEVnF,MACNY,EAAI8H,EAAS1I,IACXuP,YAAc3O,EAAE4O,cAAgBF,EAAa,KAAOvH,GACtDN,GAAKgI,IAAI7O,EAAG,CAAC8O,MAAK,EAAMC,WAAaL,EAAa,OAAS,SAe/C,SAAdM,GAAuB7P,EAASwN,GAC/BxN,EAAU0H,GAAKoI,MAAMC,QAAQ/P,GAAS,GACtCwN,EAAOA,GAAQ,OAadwC,EAAcC,EAAeC,EAAeC,EAAUC,EAAgBC,EAZnEC,EAAUC,SAASvP,cAAc,OACpCK,EAAQiP,EAAQjP,MAChBmP,EAAOxQ,EAAQyQ,WACfxK,EAAY,EACZD,EAAa,EACb0K,EAAU1Q,EAAQgE,UAClB2M,EAAW3Q,EAAQmE,WACnByM,EAAc5Q,EAAQ4Q,YACtBC,EAAe7Q,EAAQ6Q,aACvBC,EAAgB,EAChBC,EAAU,EACVC,EAAS,EAENC,IAAgC,IAAjBzD,EAAK0D,SACvBd,EAAiB,eACjBC,EAAe,WACLjO,IACVgO,EAAiB,aACjBC,EAAe,YAEXrM,UAAY,SAAS6D,EAAOsJ,OAC3BC,UAAUhM,cACNiB,KAAKH,WAETA,KAAK2B,EAAOsJ,SAEbhN,WAAa,SAAS0D,EAAOsJ,OAC5BC,UAAUhM,cACNiB,KAAKF,YAETA,MAAM0B,EAAOsJ,SAEdhL,KAAO,SAAS0B,EAAOsJ,OACtBC,UAAUhM,eACLpF,EAAQmE,WAAa6B,OAE3BqL,EAAMrR,EAAQmE,WAAawM,EAC9BW,EAAYtL,MACF,EAANqL,GAAWA,GAAO,KAAOF,SAC7BR,EAAW3Q,EAAQmE,WACnBuD,GAAK6J,aAAalL,KAAM,CAACF,KAAK,EAAGhC,WAAW,SACvCgC,MAAMwK,QACPnD,EAAKgE,QACRhE,EAAKgE,WAIP3J,GAASA,GACG,GACX7B,EAAc6B,EAAQ,GAAO,EAC7BA,EAAQ,GACUkJ,EAARlJ,GACV7B,EAAc6B,EAAQkJ,EAAW,EACjClJ,EAAQkJ,GAER/K,EAAa,GAEVA,GAAcsL,KACZjL,KAAKoL,QACTpQ,EAAMe,GAAkBgO,GAAkBpK,EAAa,OAASC,EAAYoK,GAE3C,GAA9BrK,EAAa8K,IAChBzP,EAAMqQ,aAAgB1L,EAAa8K,EAAgB,OAGrD9Q,EAAQmE,WAAqB,EAAR0D,EACrB8I,EAAW3Q,EAAQmE,iBAEf+B,IAAM,SAAS2B,EAAOsJ,OACrBC,UAAUhM,eACLpF,EAAQgE,UAAYiC,OAE1BoL,EAAMrR,EAAQgE,UAAY0M,EAC7BY,EAAYrL,MACF,EAANoL,GAAWA,GAAO,KAAOF,SAC7BT,EAAU1Q,EAAQgE,UAClB0D,GAAK6J,aAAalL,KAAM,CAACH,IAAI,EAAGlC,UAAU,SACrCkC,KAAKwK,QACNlD,EAAKgE,QACRhE,EAAKgE,WAIP3J,GAASA,GACG,GACX5B,EAAa4B,EAAQ,GAAO,EAC5BA,EAAQ,GACUmJ,EAARnJ,GACV5B,EAAa4B,EAAQmJ,EAAU,EAC/BnJ,EAAQmJ,GAER/K,EAAY,GAETA,GAAaqL,KACXjL,KAAKoL,QACTpQ,EAAMe,GAAkBgO,GAAkBpK,EAAa,OAASC,EAAYoK,IAG9ErQ,EAAQgE,UAAoB,EAAR6D,EACpB6I,EAAU1Q,EAAQgE,gBAGd2N,aAAe,kBAAMX,QACrBY,cAAgB,kBAAMb,QAEtBc,QAAU,eACdrB,EAAOF,EAAQG,WACRD,GACNL,EAAWK,EAAKsB,YAChB9R,EAAQmB,YAAYqP,GACpBA,EAAOL,EAEJnQ,IAAYsQ,EAAQpQ,YACvBF,EAAQkD,YAAYoN,SAGjByB,OAAS,eACbvB,EAAOxQ,EAAQyQ,cACFH,QAGNE,GACNL,EAAWK,EAAKsB,YAChBxB,EAAQnP,YAAYqP,GACpBA,EAAOL,EAERnQ,EAAQmB,YAAYmP,QACf0B,mBAEDA,UAAY,SAASb,OAExBrM,EAAIpE,EAAGC,EADJsR,EAAgBjS,EAAQ2M,cAAgBqD,EAE5CU,EAAU1Q,EAAQgE,UAClB2M,EAAW3Q,EAAQmE,WACf8N,GAAgBjS,EAAQgN,eAAiBiD,GAAiBK,EAAQ4B,eAAiBhC,GAAiBU,IAAgB5Q,EAAQ4Q,aAAeC,IAAiB7Q,EAAQ6Q,eAAiBM,KAGrLlL,GAAaD,KAChBtF,EAAI2F,KAAKF,OACTxF,EAAI0F,KAAKH,WACJC,MAAMnG,EAAQmE,iBACd+B,KAAKlG,EAAQgE,YAEnBc,EAAKsH,GAAkBpM,GAElBiS,IAAgBd,IACpB9P,EAAM8Q,QAAU,QAChB9Q,EAAMoL,MAAQ,OACdpL,EAAMqQ,aAAe,OACrBZ,EAAgB5I,KAAKuD,IAAI,EAAGzL,EAAQ4Q,YAAc5Q,EAAQ2M,gBAGzDmE,GAAiBsB,WAAWtN,EAAGuN,cAAgBC,EAAgBF,WAAWtN,EAAG4M,cAAgB,KAG/FrQ,EAAM8Q,QAAU,eAChB9Q,EAAMoD,SAAW,WACjBpD,EAAMkR,SAAW,UACjBlR,EAAMmR,cAAgB,MACtBnR,EAAMoR,UAAY,cAClBpR,EAAMoL,MAAQ,OACdpL,EAAMqQ,aAAeZ,EAAgB,KAEjCwB,IACHjR,EAAMqR,cAAgB5N,EAAG4N,eAE1B1C,EAAehQ,EAAQ2M,YACvBsD,EAAgBjQ,EAAQgN,aACxB4D,EAAc5Q,EAAQ4Q,YACtBC,EAAe7Q,EAAQ6Q,aACvBE,EAAU/Q,EAAQ4Q,YAAcZ,EAChCgB,EAAShR,EAAQ6Q,aAAeZ,EAChCC,EAAgBI,EAAQ4B,aACxB7Q,EAAM8Q,QAAU,SACZzR,GAAKC,UACHwF,KAAKzF,QACLwF,IAAIvF,WAGN2P,QAAUA,OACVtQ,QAAUA,OACVyR,OAAQ,OACRM,SAEM,SAAZY,GAAYC,MACPpL,KAAmB+I,SAAS5N,KAAM,KACjCkQ,EAAMrQ,QAAUA,OAAOsQ,UAC3BhR,GAAOU,OACPrC,GAAOoQ,SACPxO,GAAc5B,GAAKuC,gBACnBV,EAAQ7B,GAAKwC,KACboQ,EAAW3K,GAAe,OAC1B4K,KAAqBxQ,OAAOyQ,cAC5BC,GAAkB9K,GAAe,QACjB/G,MAAMC,QAAU,qGAChC6R,GAAkD,SAAjCD,GAAgB7R,MAAM+R,OAAoB,OAAS,OACpEC,GAAcR,IAA2D,IAApDA,EAAIS,UAAUhP,cAAciP,QAAQ,WACzDC,GAAmB,iBAAkBzR,IAAiB,gBAAiBD,IAAW+Q,IAA6B,EAArBA,EAAIY,gBAA6C,EAAvBZ,EAAIa,kBAEnHC,EAAMvL,GAAe,OACxBwL,EAAQxL,GAAe,OACvByL,EAAaD,EAAMvS,MACnB4D,EAASjD,EAEV6R,EAAW1B,QAAU,eACrB0B,EAAWpP,SAAW,WACtBkP,EAAItS,MAAMC,QAAU,sEACpBqS,EAAIxS,YAAYyS,GAChB3O,EAAO9D,YAAYwS,GACnBG,EAAOF,EAAM1B,aAAe,GAAKyB,EAAI9C,aACrC5L,EAAO/B,YAAYyQ,GAZpBrB,EAaQwB,EAER7J,GAAqB,SAAS8J,WACzBC,EAAWD,EAAME,MAAM,KAC1BC,IAAa,kBAAmBnB,EAAW,kDAAoD,oBAAqBA,EAAW,0DAA4DgB,GAAOE,MAAM,MACxM3L,EAAM,GACNrI,EAAI,GACS,IAALA,GACRqI,EAAI0L,EAAS/T,IAAMiU,EAAUjU,GAC7BqI,EAAI4L,EAAUjU,IAAM+T,EAAS/T,OAI7B8B,GAAYgI,iBAAiB,OAAQ,KAAMoK,OAAOC,eAAe,GAAI,UAAW,CAC/E7Q,IAAK,eACJ2G,EAAmB,MAGpB,MAAOrJ,WACFyH,EAjBa,CAkBnB,6CACFuB,GAAa1J,GAAM,cAAe6H,IAClC6B,GAAa/H,GAAM,YAAakG,IAChChG,GAASA,EAAM+H,iBAAiB,aAAc/B,IAC9C6B,GAAa1J,GAAM,cAAe,eAC5B,IAAIqI,KAAK6L,GACTA,GAAQ7L,GAAG8L,WACdD,GAAQ7L,GAAG+L,YAId7M,GAAO8M,GAAe/M,IA5CL,IAKfqM,EAJGH,EACHC,EACAC,EACA5O,EA0CCyC,IACH+M,GAAgB/M,GAAKgN,QAAQC,QAC7BC,GAAWlN,GAAKmN,KAAK9G,SAAW,aAChC5B,EAAezE,GAAKoI,MAAMgF,YAC1B1S,EAAiB+J,EAAa/J,GAC9BC,GAAuB8J,EAAa9J,IACpC8K,GAAWzF,GAAKoI,MAAMC,QACtBgF,GAAiBrN,GAAKmN,KAAKG,cAC3B/D,IAAgB9E,EAAa,gBACnByG,GACVqC,QAAQC,KAAK,yCA9kBhB,IAAIxN,GAAM5F,GAAM3B,GAAM4B,GAAaC,EAAO+Q,EAAUG,GAAiBsB,GAAcrI,EAAcgB,GAAUjD,EAAkBsJ,GAAgBvJ,GAAmBU,GAAkB0I,GAAYoB,GAAetB,GAAgBH,GAAkB4B,GAAUG,GA0UxP9D,EAAaqB,IAzUbzH,GAAa,EAObzI,EAAiB,YACjBC,GAAuB,kBAEvB8S,GAAWrG,MAAMsG,QAKjBC,GAAW,IAAMnN,KAAKoN,GACtBC,GAAU,KACVtT,EAAkB,IAAIQ,GACtB+S,GAAWC,KAAKC,KAAQ,kBAAM,IAAID,MAAOE,WACzC5M,GAAe,GACfsL,GAAU,GACVuB,GAAe,EACfzG,EAAmB,wCACnB0G,GAAgB,EAChBC,GAAS,GACTlK,GAAe,GAoGfV,GAAU,SAAVA,QAAUrK,WAASA,GAAKA,IAAMkB,IAA8B,IAAflB,EAAE6D,UAAkB7D,IAAMV,GAAKwC,MAAQ9B,IAAMiB,IAASjB,EAAE6D,UAAa7D,EAAEX,aAsCpHsM,EAAY,GA8CZuJ,GAAU,GACV9H,EAAoB,SAApBA,kBAAqBjO,EAAS+N,GAC7BA,EAAUZ,GAASY,GAAS,OAG3B5H,EAAMuG,EAAOxG,EAAK4G,EAAQ/G,EAAQiQ,EAAIC,EAAIC,EAAIC,EAAIC,EAAM3J,EAAOI,EAAQ/H,EAFpEuR,EAASrW,EAAQyF,SAAWzF,EAAQoE,gBACvC9B,EAAMtC,EAAQuC,eAAiBpC,MAE5BH,IAAY8B,GACfoE,EAAMpC,GAAiBxB,GAEvBoK,GADAvG,EAAOlC,GAAkB3B,KACTA,EAAII,gBAAgBiK,aAAe3M,EAAQ4M,YAActK,EAAIK,KAAKgK,aAAe,GACjGG,EAAS5G,IAASlG,EAAQ+M,aAAe,GAAK,GAAKzK,EAAII,gBAAgBsK,aAAgB1K,EAAII,gBAAgBsK,aAAehN,EAAQ+M,aAAezK,EAAIK,KAAKqK,cAAgB,OACpK,CAAA,GAAIe,IAAYjM,IAAQiG,EAAagG,UACpC/N,EAAQqH,wBAEflB,EAAOD,EAAO,EACVmQ,GAEH5J,GADA2J,EAAOpW,EAAQyF,WACFgH,MACbI,EAASuJ,EAAKvJ,SAEV7M,EAAQsW,UAAYF,EAAOpW,EAAQsW,QAAQ5Q,WAC9CS,EAAOiQ,EAAK1V,GAAK,EACjBwF,EAAMkQ,EAAKzV,GAAK,EAChB8L,EAAQ2J,EAAK3J,MACbI,EAASuJ,EAAKvJ,QAEVJ,IAEJ2J,EAAwB,gBADxBtR,EAAKsH,GAAkBpM,IACbyS,UACVhG,GAAS2F,WAAWtN,EAAG2H,QAAUzM,EAAQ2M,aAAe,IAAMyJ,EAAO,EAAIhE,WAAWtN,EAAGyR,iBAAmBnE,WAAWtN,EAAG0R,mBACxH3J,GAAUuF,WAAWtN,EAAG+H,SAAW7M,EAAQgN,cAAgB,IAAMoJ,EAAO,EAAIhE,WAAWtN,EAAG2R,gBAAkBrE,WAAWtN,EAAG4R,sBAG5HhK,EAAQD,EACRK,EAASD,SAEN7M,IAAY+N,EACR,CAAC5H,KAAKA,EAAMD,IAAIA,EAAKuG,MAAOC,EAAQvG,EAAM0G,OAAQC,EAAS5G,IAGnE8P,GADAjQ,EAASiB,gBAAgB+G,GAAS,GAAMlI,SAASmB,gBAAgBhH,KACrD6G,MAAM,CAACnG,EAAEyF,EAAMxF,EAAEuF,IAC7B+P,EAAKlQ,EAAOc,MAAM,CAACnG,EAAEgM,EAAO/L,EAAEuF,IAC9BgQ,EAAKnQ,EAAOc,MAAM,CAACnG,EAAEgM,EAAO/L,EAAEmM,IAC9BqJ,EAAKpQ,EAAOc,MAAM,CAACnG,EAAEyF,EAAMxF,EAAEmM,IAGtB,CAAC3G,KAFRA,EAAO+B,KAAKgG,IAAI8H,EAAGtV,EAAGuV,EAAGvV,EAAGwV,EAAGxV,EAAGyV,EAAGzV,GAEjBwF,IADpBA,EAAMgC,KAAKgG,IAAI8H,EAAGrV,EAAGsV,EAAGtV,EAAGuV,EAAGvV,EAAGwV,EAAGxV,GACN8L,MAAOvE,KAAKuD,IAAIuK,EAAGtV,EAAGuV,EAAGvV,EAAGwV,EAAGxV,EAAGyV,EAAGzV,GAAKyF,EAAM0G,OAAQ3E,KAAKuD,IAAIuK,EAAGrV,EAAGsV,EAAGtV,EAAGuV,EAAGvV,EAAGwV,EAAGxV,GAAKuF,KAuVrHyQ,iCAOL5M,iBAAA,0BAAiBtJ,EAAMwK,OAClB2L,EAAOvQ,KAAKqH,WAAWjN,KAAU4F,KAAKqH,WAAWjN,GAAQ,KACvDmW,EAAKrD,QAAQtI,IAClB2L,EAAKjT,KAAKsH,MAIZZ,oBAAA,6BAAoB5J,EAAMwK,OACrB2L,EAAOvQ,KAAKqH,WAAWjN,GAC1BR,EAAK2W,GAAQA,EAAKrD,QAAQtI,GACrB,GAALhL,GAAW2W,EAAKtN,OAAOrJ,EAAG,MAG5B4N,cAAA,uBAAcpN,OACT8M,gBACHlH,KAAKqH,WAAWjN,IAAS,IAAIuI,QAAQ,SAAAiC,UAAwE,IAA3DA,EAAS+D,KAAK6H,EAAM,CAACpW,KAAMA,EAAMqK,OAAQ+L,EAAK/L,WAAwByC,GAAS,KAC3HA,6CArBIzC,QACN4C,WAAa,QACb5C,OAASA,GAAUzE,YA+BbyQ,6HAAkBH,aAstCvBI,SAAP,kBAAgBlC,GACfnN,GAAOmN,EACPlC,gBAGMqE,OAAP,gBAAcC,EAASzJ,UACtBgH,IAAgB7B,IAAU,GACnBxF,GAAS8J,GAASC,IAAI,SAAApM,UAAU,IAAIgM,UAAUhM,EAAQ0C,gBAGvDjK,IAAP,aAAWuH,UACHuJ,IAASlH,GAASrC,GAAQ,IAAM,IAAIqM,sBAGrCC,cAAP,gCACS5B,KAAaK,IAAiB,eAGhCwB,QAAP,iBAAeC,EAAMC,EAAMC,MACtBF,IAASC,SACL,MAMPE,EAASC,EAAMC,EAJZC,EAAKrL,GAAW+K,GACnBO,EAAKtL,GAAWgL,GACdrR,EAA4C0R,EAA5C1R,IAAKC,EAAuCyR,EAAvCzR,KAAMuG,EAAiCkL,EAAjClL,MAAOI,EAA0B8K,EAA1B9K,OAAQL,EAAkBmL,EAAlBnL,MAAOI,EAAW+K,EAAX/K,OACnCiL,EAAaD,EAAG1R,KAAOuG,GAASmL,EAAGnL,MAAQvG,GAAQ0R,EAAG3R,IAAM4G,GAAU+K,EAAG/K,OAAS5G,SAE/E4R,IAAcN,GACTM,GAETH,GAA8C,KAAlCH,EAAY,IAAIjE,QAAQ,KACpCiE,EAAYpF,WAAWoF,IAAc,GACrCC,EAAU,CAACtR,KAAM+B,KAAKuD,IAAItF,EAAM0R,EAAG1R,MAAOD,IAAKgC,KAAKuD,IAAIvF,EAAK2R,EAAG3R,OACxDuG,MAAQvE,KAAKgG,IAAIxB,EAAOmL,EAAGnL,OAAS+K,EAAQtR,KACpDsR,EAAQ5K,OAAS3E,KAAKgG,IAAIpB,EAAQ+K,EAAG/K,QAAU2K,EAAQvR,MACnDuR,EAAQhL,MAAQ,GAAKgL,EAAQ5K,OAAS,KAGtC8K,EAGalL,EAAQI,GAFxB2K,GAAa,OACbE,EAAOD,EAAQhL,MAAQgL,EAAQ5K,SACe6K,GAAQG,EAAGpL,MAAQoL,EAAGhL,OAAS2K,EAEtEC,EAAQhL,MAAQ+K,GAAaC,EAAQ5K,OAAS2K,mCA/vC3C1M,EAAQ0C,+BAEnBgH,IAAgB7B,GAAU,GAC1B7H,EAASqC,GAASrC,GAAQ,KACrBiN,OAAShD,IAAkBA,GAAejK,EAAQ,sBAEtD2J,GADIA,IACY/M,GAAKgN,QAAQC,UAEzBnH,KAAOA,EAAOnF,GAAMmF,GAAQ,MAC5B1C,OAASA,IACTpK,EAAIsX,EAAKrX,EAAIqX,EAAKC,SAAW,IAC7BC,eAAiB9F,WAAW5E,EAAK0K,iBAAmB,IACpDC,eAAiBC,MAAM5K,EAAK2K,gBAAkB,EAAI/F,WAAW5E,EAAK2K,iBAAmB,IACrFE,SAAW7K,EAAK6K,WAChBC,WAAa9K,EAAK8K,YAAc,IAChCC,WAAa,OACbC,oBAAsBhL,EAAKgL,kBAEhC9Q,GAAK+Q,YAAY3N,EAAQ,KAuBT,SAAf4N,GAAgBxM,EAAUyM,UAASvG,WAAWwG,GAAQrV,IAAIuH,EAAQoB,EAAUyM,IAI5D,SAAhBE,GAAgBhY,UAEfyJ,GAAgBzJ,GAChBA,EAAEiY,0BAA4BjY,EAAEiY,4BACzB,EAIC,SAATC,GAASC,MACJC,EAAKX,YAAcW,EAAKC,aAAeC,IAAyBC,GAAQ,KAG1EnU,EAAQoU,EAAQC,EAAMC,EAAUC,EAAUC,EAASC,EAASC,EAFzD9Y,EAAIiK,EACP8O,EAAqC,GAAlBX,EAAKX,eAEzBa,IAAwB,EACxBvN,GAAa5H,UAAkC,MAApBlC,GAAKiC,YAAuBjC,GAAKiC,YAAqD,MAAtC8V,GAASnX,gBAAgBsB,UAAqB6V,GAASnX,gBAAgBsB,UAAY6V,GAASlX,KAAKqB,UAC5K4H,GAAazH,WAAmC,MAApBrC,GAAKoC,YAAuBpC,GAAKoC,YAAsD,MAAvC2V,GAASnX,gBAAgByB,WAAsB0V,GAASnX,gBAAgByB,WAAa0V,GAASlX,KAAKwB,WAC/KoV,EAAWN,EAAKM,SAAW3N,GAAazH,WACxCqV,EAAWP,EAAKO,SAAW5N,GAAa5H,UACjCnD,IAAMwY,GAEZpU,GADAoU,EAASnO,GAAQrK,EAAEX,aACD0L,GAAe/K,EAAEX,WACnCoZ,EAAOD,EAAS,CAACvM,OAAO5E,KAAKuD,IAAI1J,GAAYiL,aAAclL,GAAKiL,aAAe,GAAIL,MAAOxE,KAAKuD,IAAI1J,GAAY4K,YAAa7K,GAAK8K,YAAc,GAAIzG,KAAK,EAAGD,IAAI,GAAKjB,EAAOoC,wBAC3KoS,EAAUC,EAAU,EAChBI,KACHH,EAAM1U,EAAO6G,cAAgB7G,EAAOjB,WAC1B,EACT0V,EAAUC,EACAH,EAAWF,EAAKxM,OAASiN,IAA0BJ,GAC7DR,IAAwB,EACxBO,EAAUxR,KAAKgG,IAAIyL,EAAMC,GAAoB,EAAI1R,KAAKuD,IAAI,EAAI6N,EAAKxM,OAAS0M,GAAaO,IAA2B,IAC1GP,EAAWF,EAAKpT,IAAM8T,IAAuB/U,EAAOjB,YAC9DmV,IAAwB,EACxBO,GAAWxR,KAAKgG,IAAIjJ,EAAOjB,UAAY4V,GAAoB,EAAI1R,KAAKuD,IAAI,EAAI+N,EAAWF,EAAKpT,KAAQ8T,IAAwB,IAEzHN,IACHzU,EAAOjB,WAAa0V,IAGlBO,KACHN,EAAM1U,EAAO4G,cAAgB5G,EAAOd,YAC1B,EACTsV,EAAUE,EACAJ,EAAWD,EAAK5M,MAAQwN,IAAyBP,GAC3DR,IAAwB,EACxBM,EAAUvR,KAAKgG,IAAIyL,EAAMC,GAAoB,EAAI1R,KAAKuD,IAAI,EAAI6N,EAAK5M,MAAQ6M,GAAaW,IAA0B,IACxGX,EAAWD,EAAKnT,KAAOgU,IAAwBlV,EAAOd,aAChEgV,IAAwB,EACxBM,GAAWvR,KAAKgG,IAAIjJ,EAAOd,WAAayV,GAAoB,EAAI1R,KAAKuD,IAAI,EAAI8N,EAAWD,EAAKnT,MAASgU,IAAyB,IAE5HV,IACHxU,EAAOd,YAAcsV,IAInBJ,IAAWI,GAAWC,KACzB5X,GAAKsY,SAASnV,EAAOd,WAAYc,EAAOjB,WACxCqW,GAAmBpB,EAAKM,SAAWE,EAASR,EAAKO,SAAWE,IAE7D7Y,EAAIoE,KAGFmU,EAAO,KACL1Y,EAAQuY,EAARvY,EAAGC,EAAKsY,EAALtY,EACJ2Z,GACHrB,EAAKsB,OAAS7Z,EAAI0R,WAAWwG,GAAQX,UACrCgB,EAAKhB,SAAWvX,EAChBkY,GAAQX,SAAWvX,EAAI,MACvBkY,GAAQlV,gBAAgB,EAAGkV,KAEvB4B,GACCV,IACHb,EAAKwB,OAAS9Z,EAAI6Z,EAAYtU,MAC9BsU,EAAYtU,IAAIvF,IAEbsZ,IACHhB,EAAKsB,OAAS7Z,EAAI8Z,EAAYrU,OAC9BqU,EAAYrU,KAAKzF,KAERga,GACNZ,IACHb,EAAKwB,OAAS9Z,EAAIyR,WAAWwG,GAAQjY,GACrCiY,GAAQjY,EAAIA,EAAI,MAEbsZ,IACHhB,EAAKsB,OAAS7Z,EAAI0R,WAAWwG,GAAQlY,GACrCkY,GAAQlY,EAAIA,EAAI,MAEjBkY,GAAQlV,gBAAgB,EAAGkV,MAEvBkB,IACHb,EAAKwB,OAAS9Z,EAAIyR,WAAWtH,EAAOzJ,MAAM6E,KAAO,GACjD4E,EAAOzJ,MAAM6E,IAAMvF,EAAI,MAEpBsZ,IACHhB,EAAKsB,OAAS7Z,EAAI0R,WAAWtH,EAAOzJ,MAAM8E,MAAQ,GAClD2E,EAAOzJ,MAAM8E,KAAOzF,EAAI,QAIvBia,GAAoB3B,GAAmB4B,MAC1CA,GAAgB,KACZvN,GAAe4L,EAAM,OAAQ,YAC5BgB,IACHhB,EAAKvY,GAAKuY,EAAKsB,QAEZT,IACHb,EAAKtY,GAAKsY,EAAKwB,QAEhB1B,IAAO,IAER6B,GAAgB,GAGlBxB,GAAQ,EAIA,SAATyB,GAAUC,EAAcC,OAEtBC,EAAclW,EADTpE,EAASuY,EAATvY,EAAGC,EAAMsY,EAANtY,EAEJmK,EAAOzH,QACXuV,GAAUlR,GAAKmN,KAAKoG,SAASnQ,IAE9B8N,GAAQtV,SAAWoE,GAAK+Q,YAAY3N,EAAQ,KACxC4P,GACHzB,EAAKvY,EAAI0R,WAAWwG,GAAQlY,GAC5BuY,EAAKtY,EAAIyR,WAAWwG,GAAQjY,IAClB2Z,EACVrB,EAAKvY,EAAIuY,EAAKhB,SAAW7F,WAAWwG,GAAQX,UAClCuC,GACVvB,EAAKtY,EAAI6Z,EAAYtU,MACrB+S,EAAKvY,EAAI8Z,EAAYrU,SAErB8S,EAAKtY,EAAIyR,WAAWtH,EAAOzJ,MAAM6E,MAASpB,EAAKsH,GAAkBtB,KAAYhG,EAAGoB,MAAS,EACzF+S,EAAKvY,EAAI0R,WAAWtH,EAAOzJ,MAAM8E,OAASrB,GAAM,IAAIqB,OAAS,IAEzD+U,GAASC,GAASC,KAAYL,IAAa9B,EAAKC,YAAcD,EAAKoC,cACnED,IACHtF,GAAOpV,EAAIuY,EAAKvY,EAChBoV,GAAOnV,EAAIsY,EAAKtY,GAChBqa,EAAeI,EAAOtF,KACLpV,IAAMuY,EAAKvY,IAC3BuY,EAAKvY,EAAIsa,EAAata,EACtB0Y,GAAQ,GAEL4B,EAAara,IAAMsY,EAAKtY,IAC3BsY,EAAKtY,EAAIqa,EAAara,EACtByY,GAAQ,IAGN8B,IACHF,EAAeE,EAAMjC,EAAKvY,MACLuY,EAAKvY,IACzBuY,EAAKvY,EAAIsa,EACLV,IACHrB,EAAKhB,SAAW+C,GAEjB5B,GAAQ,GAGN+B,KACHH,EAAeG,EAAMlC,EAAKtY,MACLsY,EAAKtY,IACzBsY,EAAKtY,EAAIqa,GAEV5B,GAAQ,IAGVA,GAASL,IAAO,GACX+B,IACJ7B,EAAKsB,OAAStB,EAAKvY,EAAIA,EACvBuY,EAAKwB,OAASxB,EAAKtY,EAAIA,EACvB0M,GAAe4L,EAAM,cAAe,kBAItB,SAAhBqC,GAAiB3M,EAAMT,EAAKzC,EAAKlD,UACrB,MAAP2F,IACHA,GAAOqH,IAEG,MAAP9J,IACHA,EAAM8J,IAEH3N,EAAY+G,GACR,SAAA4M,OACFC,EAAiBvC,EAAK3E,UAAgB,EAAI2E,EAAKd,eAAb,SAC/BxJ,EAAKK,KAAKiK,GAAWxN,EAAJ8P,EAAU9P,GAAO8P,EAAI9P,GAAO+P,EAAiBD,EAAIrN,EAAOA,GAAOqN,EAAIrN,GAAOsN,EAAgBD,GAAKhT,GAAUA,GAG/H4M,GAASxG,GACL,SAAA4M,WAILzH,EAAKzC,EAHFpR,EAAI0O,EAAKvJ,OACZqW,EAAU,EACVC,EAASnG,IAEI,IAALtV,IAERoR,GADAyC,EAAMnF,EAAK1O,IACCsb,GACF,IACTlK,GAAOA,GAEJA,EAAMqK,GAAiBxN,GAAP4F,GAAcA,GAAOrI,IACxCgQ,EAAUxb,EACVyb,EAASrK,UAGJ1C,EAAK8M,IAGPrD,MAAMzJ,GAAQ,SAAA4M,UAAKA,GAAI,kBAAM5M,EAAOpG,GAuD1B,SAAlBoT,SACKC,EAAQC,EAAclN,EAAMmN,EAChCC,GAAY,EACRvB,GACHA,EAAYxI,YACZiH,EAAK9K,KAAOA,GAAQqM,EAAY5I,gBAChCqH,EAAK5K,KAAOA,GAAQmM,EAAY7I,eAChCsH,EAAK3K,KAAOA,EAAO2K,EAAKzK,KAAOA,EAAO,EACtCuN,GAAY,GACAvO,EAAKoO,SACjBA,EAAS9N,GAAWN,EAAKoO,OAAQ9Q,EAAO5K,YACpCoa,GACHrB,EAAK9K,KAAOA,EAAOyN,EAAOzV,KAC1B8S,EAAK3K,KAAOA,EAAOsN,EAAOzV,KAAOyV,EAAOnP,MACxCwM,EAAK5K,KAAOA,EAAO4K,EAAKzK,KAAOA,EAAO,GAC3BzG,EAAayF,EAAKoO,OAAOtN,OAAUvG,EAAayF,EAAKoO,OAAOpN,OAOvEqN,EAAe/N,GAAWhD,EAAQA,EAAO5K,YACzC+Y,EAAK9K,KAAOA,EAAOjG,KAAKC,MAAMuQ,GAAasD,EAAO,MAAQJ,EAAOzV,KAAO0V,EAAa1V,MACrF8S,EAAK5K,KAAOA,EAAOnG,KAAKC,MAAMuQ,GAAauD,EAAO,MAAQL,EAAO1V,IAAM2V,EAAa3V,KACpF+S,EAAK3K,KAAOA,EAAOpG,KAAKC,MAAMgG,GAAQyN,EAAOnP,MAAQoP,EAAapP,QAClEwM,EAAKzK,KAAOA,EAAOtG,KAAKC,MAAMkG,GAAQuN,EAAO/O,OAASgP,EAAahP,WAVnE+O,EAASpO,EAAKoO,OACd3C,EAAK9K,KAAOA,EAAOyN,EAAOzN,KAC1B8K,EAAK5K,KAAOA,EAAOuN,EAAOvN,KAC1B4K,EAAK3K,KAAOA,EAAOsN,EAAOtN,KAC1B2K,EAAKzK,KAAOA,EAAOoN,EAAOpN,MAQhBF,EAAPH,IACH8K,EAAK9K,KAAOG,EACZ2K,EAAK3K,KAAOA,EAAOH,EACnBA,EAAO8K,EAAK9K,MAEFK,EAAPH,IACH4K,EAAK5K,KAAOG,EACZyK,EAAKzK,KAAOA,EAAOH,EACnBA,EAAO4K,EAAK5K,MAETiM,IACHrB,EAAK7K,YAAcD,EACnB8K,EAAK1K,YAAcD,GAEpByN,GAAY,GAETvO,EAAK0O,WACRvN,GAA0B,IAAlBnB,EAAK0O,SAAsB1O,EAAKmB,MAAQ,GAAMnB,EAAK0O,SAC3DJ,EAAa3G,GAASxG,IAAS/G,EAAY+G,GACvC2L,GACHY,EAAQI,GAAeQ,EAAYnN,EAAOA,EAAKsJ,SAAW9J,EAAMG,EAAM,GACtE6M,EAAQ,MAEJxM,EAAKwN,OACRf,EAxGiB,SAArBgB,mBAAsBzN,EAAMR,EAAMG,EAAMD,EAAMG,EAAM6N,EAAQ9T,UAC3D8T,EAAUA,GAAUA,EAAS9G,GAAW8G,EAASA,EAAS9G,GACtD3N,EAAY+G,GACR,SAAA7H,OAILyG,EAAQ+O,EAAIC,EAHTf,EAAiBvC,EAAK3E,UAAgB,EAAI2E,EAAKd,eAAb,EACrCzX,EAAIoG,EAAMpG,EACVC,EAAImG,EAAMnG,SAEXmG,EAAMpG,EAAIA,EAAS4N,EAAJ5N,EAAW4N,GAAQ5N,EAAI4N,GAAQkN,EAAiB9a,EAAIyN,EAAQA,GAAQzN,EAAIyN,GAAQqN,EAAgB9a,EAC/GoG,EAAMnG,EAAIA,EAAS6N,EAAJ7N,EAAW6N,GAAQ7N,EAAI6N,GAAQgN,EAAiB7a,EAAI0N,EAAQA,GAAQ1N,EAAI0N,GAAQmN,EAAgB7a,GAC/G4M,EAASoB,EAAKK,KAAKiK,EAAMnS,MACVA,IACdA,EAAMpG,EAAI6M,EAAO7M,EACjBoG,EAAMnG,EAAI4M,EAAO5M,GAEH,IAAX4H,IACHzB,EAAMpG,GAAK6H,EACXzB,EAAMnG,GAAK4H,GAER8T,EAAS9G,KACZ+G,EAAKxV,EAAMpG,EAAIA,EACf6b,EAAKzV,EAAMnG,EAAIA,EACS0b,EAApBC,EAAKA,EAAKC,EAAKA,IAClBzV,EAAMpG,EAAIA,EACVoG,EAAMnG,EAAIA,IAGLmG,GAGLqO,GAASxG,GACL,SAAAnG,WAIL9H,EAAGC,EAAGmG,EAAO0V,EAHVvc,EAAI0O,EAAKvJ,OACZqW,EAAU,EACVgB,EAAUlH,IAEG,IAALtV,IAIRuc,GAFA9b,GADAoG,EAAQ6H,EAAK1O,IACHS,EAAI8H,EAAE9H,GAELA,GADXC,EAAImG,EAAMnG,EAAI6H,EAAE7H,GACGA,GACR8b,IACVhB,EAAUxb,EACVwc,EAAUD,UAGJC,GAAWJ,EAAU1N,EAAK8M,GAAWjT,GAGxC,SAAA+S,UAAKA,GAuDAa,CAAoBN,EAAYnN,EAAOA,EAAKwN,OAAShO,EAAMG,EAAMD,EAAMG,EAAMG,EAAK0N,OAAQ7B,GAAe,EAAI,IAElHP,IACHiB,EAAQI,GAAeQ,EAAYnN,EAAOA,EAAKjO,GAAKiO,EAAKxI,MAAQwI,EAAKxK,WAAagK,EAAMG,EAAMkM,GAAe,EAAI,IAE/GV,IACHqB,EAAQG,GAAeQ,EAAYnN,EAAOA,EAAKhO,GAAKgO,EAAKzI,KAAOyI,EAAK3K,UAAYqK,EAAMG,EAAMgM,GAAe,EAAI,MAOnG,SAAlBkC,KACCzD,EAAKoC,YAAa,EAClBhO,GAAe4L,EAAM,gBAAiB,mBAEpB,SAAnB0D,KACC1D,EAAKoC,YAAa,EAGT,SAAVuB,GAAWjI,EAAS/F,OACfD,EAAMmN,EAAWe,EAAOC,EACxBnI,GAAWF,KACE,IAAZE,IACHhG,EAAOnB,EAAKmB,MAAQnB,EAAK0O,UAAY,GACrCJ,EAAa3G,GAASxG,IAAS/G,EAAY+G,GAC3CgG,EAAU,CAACoI,YAAYvP,EAAKwP,iBAAmBxP,EAAKuP,YAAc,MAASzC,EAAe,GAAK,IAC3FA,EACH3F,EAAQsD,SAAWxJ,GAAcwK,EAAM6C,EAAYnN,EAAOA,EAAKsJ,SAAU3J,EAAMH,EAAM,EAAGS,IAEpFqL,IACHtF,EAAQqH,GAASvN,GAAcwK,EAAM6C,EAAYnN,EAAOA,EAAKwN,QAAUxN,EAAKjO,GAAKiO,EAAKxI,KAAMmI,EAAMH,EAAMqM,GAAe,EAAI,EAAG5L,GAA0C,MAApBqK,EAAKV,aAEtJuB,IACHnF,EAAQsH,GAASxN,GAAcwK,EAAM6C,EAAYnN,EAAOA,EAAKwN,QAAUxN,EAAKhO,GAAKgO,EAAKzI,IAAKsI,EAAMH,EAAMmM,GAAe,EAAI,EAAG5L,GAA0C,MAApBqK,EAAKV,cAErJ5J,EAAKwN,QAAWhH,GAASxG,IAAS7G,EAAU6G,EAAK,OACpDgG,EAAQsI,YAAcjB,EAAQ,IAAMC,EACpCtH,EAAQ0H,OAAS1N,EAAK0N,UAIzBpD,EAAKoC,YAAa,EAClByB,EAAuB1E,MAAM5K,EAAKsP,oBAA0E,IAAxBtP,EAAK2K,eAAwB,EAAK,EAAIc,EAAKd,eAAkB,GAAxF3K,EAAKsP,mBACzDnI,EAAQjL,WACZiL,EAAQjL,SAAW,CAAC+B,IAAKvD,KAAKuD,IAAI+B,EAAK0P,aAAe,EAAI,gBAAiB1P,EAAQA,EAAK2P,YAAc,GAAIjP,IAAOkK,MAAM5K,EAAK0P,aAA0D,IAAvBJ,GAA6BhV,EAAU6M,IAAiC,IAArBA,EAAQoI,WAAsB,EAAI,GAAzGvP,EAAK0P,YAA0GE,UAAWN,IAEtQ7D,EAAK4D,MAAQA,EAAQnV,GAAK6B,GAAGiR,GAAe1P,EAAQ,CACnD6J,QAASA,EACT/K,KAAM,aACNyT,SAAS,EACT1T,WAAY+S,GACZY,YAAaX,GACbY,SAAW/P,EAAKgQ,SAAWnQ,GAAiBwN,GAC5C4C,eAAiBjQ,EAAKgQ,SAAW,CAACvE,EAAM,gBAAiB,iBAAoBtK,GAAQA,EAAK0N,OAAU,EAAC,GAAO,GAAQ,KAEhH7O,EAAKgQ,WACLhD,IACHA,EAAY/I,OAAQ,GAErBoL,EAAM9D,OAAO,KAAK,GAAM,GACxB8B,IAAO,GAAM,GACb5B,EAAKyE,KAAOzE,EAAKvY,EACjBuY,EAAK0E,KAAO1E,EAAKtY,EACb2Z,IACHrB,EAAK2E,YAAc3E,EAAKvY,GAEzBmc,EAAMgB,KAAK,GACXhD,IAAO,GAAM,GACTL,IACHA,EAAY/I,OAAQ,KAGZsK,GACV9C,EAAK6E,cAIQ,SAAfC,GAAeC,OAEbxV,EADGyV,EAAQlY,EAEZA,EAASiB,gBAAgB8D,EAAO5K,YAAY,GACxC8d,GAAc/E,EAAK3E,YAAcvO,EAAOa,OAAOqX,GAAS,IAAIxb,MAC/D+F,EAAIyV,EAAM7X,UAAUS,MAAM,CAACnG,EAAEwd,EAAevd,EAAEwd,IAC9CpY,EAAOc,MAAM2B,EAAGA,GAChB0V,EAAgB1V,EAAE9H,EAClByd,EAAgB3V,EAAE7H,GAEfoF,EAAOa,OAAO3E,KACjB8D,EAAS,MAIY,SAAvBqY,SAIEC,EAAc3d,EAAGC,EAHd6a,EAAgB,EAAIvC,EAAKd,eAC5BmG,EAAU/W,GAAUtD,GAAkB4V,IAAY,EAClD0E,EAAUhX,GAAUzD,GAAiB+V,IAAY,EAE9Ca,IACH9B,GAAQlY,EAAIgY,GAAasD,EAAO,MAAQ,KACxCpD,GAAQjY,EAAI+X,GAAauD,EAAO,MAAQ,KACxCrD,GAAQlV,mBAETqa,IAAa,GACbhI,GAAQrV,EAAIuY,EAAKM,SAAW+E,EAC5BvI,GAAQpV,EAAIsY,EAAKO,SAAW+E,EAC5BxY,GAAUA,EAAOc,MAAMkP,GAASA,IAChCmI,EAAgBnI,GAAQrV,EACxByd,EAAgBpI,GAAQpV,EACpByY,IACHiB,GAAmBpB,EAAKM,SAAUN,EAAKO,UACvCT,IAAO,IAERyF,EAAcxX,gBAAgB8D,GAC1B0P,GACHmB,KACA8C,EAAgBjE,EAAYtU,MAC5BwY,EAAgBlE,EAAYrU,SAGxBwY,MACH9D,IAAO,GAAM,GACbc,MAEA1C,EAAK6E,cAEFxD,GACH+D,EAAevT,EAAO1G,gBAAkB,CAACwU,GAAQgG,QAAU9T,EAAOrF,UAAU/E,EAAGkY,GAAQiG,QAAU/T,EAAOrF,UAAU9E,IAAMyL,GAAkBtB,GAAQzI,KAAyB,OAAO4R,MAAM,KACxL6K,EAAiB7F,EAAK6F,eAAiB9X,gBAAgB8D,GAAQjE,MAAM,CAACnG,EAAG0R,WAAWiM,EAAa,KAAO,EAAG1d,EAAGyR,WAAWiM,EAAa,KAAO,IAC7IxD,IAAO,GAAM,GACbna,EAAIuY,EAAKM,SAAWuF,EAAepe,EAAI4d,EACvC3d,EAAIme,EAAene,EAAIsY,EAAKO,SAAW+E,EACvCG,EAAgBzF,EAAKvY,EACrB+d,EAAgBxF,EAAKtY,EAAIuH,KAAK6W,MAAMpe,EAAGD,GAAK2U,KAK5CoJ,EAAgB/F,GAAauD,EAAO,MACpCyC,EAAgBhG,GAAasD,EAAO,QAIlCD,GAAaP,IACIlN,EAAhBoQ,EACHA,EAAgBpQ,GAAQoQ,EAAgBpQ,GAAQkN,EACtCkD,EAAgBvQ,IAC1BuQ,EAAgBvQ,GAAQA,EAAOuQ,GAAiBlD,GAE5ClB,IACgB9L,EAAhBiQ,EACHA,EAAgBjQ,GAAQiQ,EAAgBjQ,GAAQgN,EACtCiD,EAAgBpQ,IAC1BoQ,EAAgBpQ,GAAQA,EAAOoQ,GAAiBjD,KAInDvC,EAAK+F,OAASN,EAAgBzW,GAAOyW,GACrCzF,EAAKgG,OAASR,EAAgBxW,GAAOwW,GAKlB,SAApBS,MACKhM,GAAgBhT,YAAeye,MAAiB1F,EAAKC,YACxDhG,GAAgBhT,WAAWgD,YAAYgQ,IAK/B,SAAViM,GAAWte,EAAGsQ,OACTlR,MACCmf,GAAWnG,EAAK3E,YAAczT,KAAkB,cAAXA,EAAEJ,MAAmC,gBAAXI,EAAEJ,MAA4B0Q,IAASqE,KAAa6J,GAAY,IAAMpV,GAAkBgP,EAAKrL,aAAanN,MAC7K6e,GAAuBze,GAAKue,GAAW9U,GAAgBzJ,WAGxD0e,EAAcZ,KACda,GAAU,EACVvG,EAAKrL,aAAe/M,EAChBoJ,GAAkBpJ,EAAEJ,OACvBgf,GAAoB5e,EAAEJ,KAAK8S,QAAQ,SAAY1S,EAAE6e,eAAiB7e,EAAEiK,OAAU+O,GAC9EhQ,GAAa4V,EAAkB,WAAYE,IAC3C9V,GAAa4V,EAAkB,YAAaG,IAC5C/V,GAAa4V,EAAkB,cAAeE,IAC9C9V,GAAagQ,GAAU,aAAc9O,MAErC0U,EAAmB,KACnB5V,GAAagQ,GAAU,YAAa+F,KAErCC,EAAgB,KACX7M,IAAsByM,IAC1B5V,GAAagQ,GAAU,UAAW8F,IAClC9e,GAAKA,EAAEiK,QAAUjB,GAAahJ,EAAEiK,OAAQ,UAAW6U,KAEpDG,EAAcC,GAAY/Q,KAAKiK,EAAMpY,EAAEiK,UAAmC,IAAxB0C,EAAKwS,iBAA6B7O,SAEnFtH,GAAahJ,EAAEiK,OAAQ,SAAU6U,IACjCtS,GAAe4L,EAAM,YAAa,eAClC5L,GAAe4L,EAAM,QAAS,WAC9B3J,GAAe2Q,GAAU,QACzBX,GAAsB,MAGvBY,KAA8BT,GAAoBxF,GAAWH,IAAkD,IAAxCb,EAAKzL,KAAK0S,2BAAwCjH,EAAKzL,KAAK2S,kBAAoBtf,IAAMA,EAAEuf,SAAqB,EAAVvf,EAAEwf,UAAuBpG,EAAS,IAAM,MAClNqF,GAAuBY,IAA8BjH,EAAKT,qBAEzDlO,GAAgBzJ,GAChBgJ,GAAa/H,GAAM,mBAAoBwI,KAEpCzJ,EAAEyf,gBACLzf,EAAI0f,EAAQ1f,EAAEyf,eAAe,GAC7BE,EAAU3f,EAAE4f,YACF5f,EAAE6f,UACZF,EAAU3f,EAAE6f,UAEZH,EAAQC,EAAU,KAEnB3V,KA3oCiB,SAApB8V,kBAAoB1X,GACnBF,GAAapF,KAAKsF,GACU,IAAxBF,GAAa3D,QAChBsC,GAAKyB,OAAOyX,IAAI9X,IAyoCf6X,CAAkB5H,IAClBoF,EAAgBlF,EAAKO,SAAW3Y,EAAEuM,MAClC8Q,EAAgBjF,EAAKM,SAAW1Y,EAAEqM,MAClCG,GAAe4L,EAAM,YAAa,gBAC9BiH,GAA6BjH,EAAKX,aACrC5M,GAAkBZ,EAAO5K,aAEtB4K,EAAO5K,aAAc+Y,EAAKX,YAAekC,GAAgBF,IAAgBxP,EAAO5K,WAAW2L,eAAkBqH,GAAgBhT,YAAe4K,EAAOrF,UACtJyN,GAAgB7R,MAAMoL,MAAQ3B,EAAO5K,WAAW0Q,YAAc,KAC9D9F,EAAO5K,WAAWiB,YAAY+R,KAE/BkL,KACAnF,EAAK4D,OAAS5D,EAAK4D,MAAMgE,OACzB5H,EAAKoC,YAAa,EAClB3T,GAAK6J,aAAaiJ,GAAe1P,EAAQgW,GAAW,GACpDtG,GAAe9S,GAAK6J,aAAazG,EAAQ,CAACsP,SAAS,IAAI,GACvDnB,EAAK4D,MAAQ5D,EAAKV,WAAa,MAC3B/K,EAAKuT,cAAiBzG,GAAiBE,IAAoC,IAArBhN,EAAKuT,eAC9DjW,EAAOzJ,MAAM2f,OAASlK,UAAUkK,UAEjC/H,EAAK3E,WAAY,EACjBqG,KAAqBnN,EAAKyT,SAAUhI,EAAKvL,WAAWwT,MACpDC,KAAqB3T,EAAKoS,SAAU3G,EAAKvL,WAAW0T,OAChC,IAAhB5T,EAAK4F,QAAoB5F,EAAK6T,iBACjCphB,EAAIggB,EAAS7a,QACC,IAALnF,GACRyH,GAAKgI,IAAIuQ,EAAShgB,GAAI,CAACmT,OAAQ5F,EAAK6T,cAAgB7T,EAAK4F,SAA8B,SAAnBD,GAA4B,WAAaA,MAG/G9F,GAAe4L,EAAM,QAAS,YA8ShB,SAAfqI,GAAezgB,MACVA,GAAKoY,EAAKC,aAAesB,EAAa,KACrCvV,EAASpE,EAAEiK,QAAUA,EAAO5K,WAC/Bqa,EAAStV,EAAOd,WAAac,EAAO8G,WACpC0O,EAASxV,EAAOjB,UAAYiB,EAAO+G,YAChCuO,GAAUE,KACT1U,GACHmY,GAAiB3D,EAASxU,EAAOtE,EAAIgZ,EAAS1U,EAAOpE,EACrDwc,GAAiB1D,EAAS1U,EAAOnE,EAAI2Y,EAASxU,EAAOrE,IAErDwc,GAAiB3D,EACjB4D,GAAiB1D,GAElBxV,EAAO8G,YAAcwO,EACrBtV,EAAO+G,YAAcyO,EACrBJ,GAAmBpB,EAAKM,SAAUN,EAAKO,YAKhC,SAAV+H,GAAU1gB,OACL2gB,EAAOhM,KACViM,EAAmBD,EAAOnC,GAAY,IACtCqC,EAAmBF,EAAOG,GAAc,GACxCC,EAAqBH,GAAmBI,IAAkBxC,GAC1DyC,EAAoB7I,EAAKrL,cAAgBqL,EAAKrL,aAAakU,iBAC3DC,EAA4BN,GAAmBO,IAAyB3C,GACxE4C,EAAUphB,EAAEqhB,WAA6B,MAAfrhB,EAAEqhB,WAAqBT,GAAmBG,MAChEA,GAAsBF,IAAqD,IAAlCzI,EAAKzL,KAAK2U,sBAAoCthB,EAAEiY,0BAC7FjY,EAAEiY,2BAEC2I,KAAqBxI,EAAKrL,eAAgBqL,EAAKrL,aAAakU,qBAAuBF,GAAsBK,IAAYF,UACpHE,GAAWL,IACdI,EAAuB3C,SAExBwC,EAAgBxC,KAGbpG,EAAK3E,WAAaoN,GAAmBD,KACnCQ,GAAYphB,EAAEuhB,QAAWX,IAAmBK,GAChDxX,GAAgBzJ,IAGb4gB,GAAoBC,GAAoBlC,IAC5C3e,GAAKA,EAAEiK,SAAWmO,EAAKrL,aAAe/M,GACtCwM,GAAe4L,EAAM,QAAS,YAIhB,SAAhBoJ,GAAgB7Z,UAAKzC,EAAS,CAACrF,EAAE8H,EAAE9H,EAAIqF,EAAOtE,EAAI+G,EAAE7H,EAAIoF,EAAOpE,EAAIoE,EAAOlF,EAAGF,EAAE6H,EAAE9H,EAAIqF,EAAOrE,EAAI8G,EAAE7H,EAAIoF,EAAOnE,EAAImE,EAAOlE,GAAK,CAACnB,EAAE8H,EAAE9H,EAAGC,EAAE6H,EAAE7H,OAr5BzIye,EAAS5E,EAAa0D,EAAeC,EAAeO,EAAeD,EAAe1C,EAAWpB,EAAiBwG,EAAiB7S,EAAMH,EAAMK,EAAMH,EAAMkS,EAAOC,EAAS1B,EAAgB1F,EAAOkJ,EAAKpH,EAAOC,EAAOC,EAAQ0E,EAAYL,EAAkB1Z,EAAQwZ,EAAaW,EAA2BL,EAAejF,EAAeiH,EAAeG,EAAsB1C,EAAqBd,EAAagB,EAvBzY/e,GAAQ+M,EAAK/M,MAAQ,OAAO6D,cAC/BoW,GAAWja,EAAK8S,QAAQ,OAAS9S,EAAK8S,QAAQ,KAC9C+G,GAA8C,IAA9B7Z,EAAK8S,QAAQ,YAC7ByI,EAAQ1B,EAAe,WAAaI,EAAS,IAAM,OACnDuB,EAAQvB,EAAS,IAAM,MACvBT,MAAaxZ,EAAK8S,QAAQ,QAAS9S,EAAK8S,QAAQ,SAAoB,WAAT9S,GAC3DqZ,MAAarZ,EAAK8S,QAAQ,QAAS9S,EAAK8S,QAAQ,QAAmB,WAAT9S,GAC1D8hB,EAAkB/U,EAAK+U,iBAAmB,EAC1CtJ,4BACAgH,EAAW9S,GAASK,EAAKgV,SAAWhV,EAAKiV,QAAU3X,GACnDgW,EAAY,GACZa,GAAc,EACdxI,IAAwB,EACxBa,GAAsBxM,EAAKwM,qBAAuB,GAClDE,GAAwB1M,EAAK0M,uBAAyB,GACtDH,GAAyBvM,EAAKuM,wBAA0B,GACxDI,GAAuB3M,EAAK2M,sBAAwB,GACpD4F,GAAcvS,EAAKkV,eAAiBxT,GACpCmQ,GAAY,EACZzG,GAAU9N,EAAOzH,OAASqE,GAAKmN,KAAKoG,SAASnQ,GAC7CvD,GAnWS,SAAXhD,SAAWvE,SACkC,UAAxCoM,GAAkBpM,GAASyE,YAG/BzE,EAAUA,EAAQE,aACkB,IAArBF,EAAQ0E,SACfH,SAASvE,WA6VNuE,CAASuG,GAEnB+O,GAAW/O,EAAOvI,eAAiBpC,GAienCwe,GAAa,SAAbA,oBAAmB1F,EAAK4D,OAAS5D,EAAK4D,MAAM8F,YA0F5C/C,GAAS,SAATA,OAAS/e,OAEP+J,EAAS2O,EAAUC,EAAUvZ,EAAGqc,EAAIC,EADjCqG,EAAgB/hB,KAEfue,IAAWzU,IAAqBsO,EAAK3E,WAAczT,MAKxD+J,GADAqO,EAAKrL,aAAe/M,GACRyf,oBAEXzf,EAAI+J,EAAQ,MACF2V,GAAS1f,EAAE4f,aAAeD,EAAS,KAC5CvgB,EAAI2K,EAAQxF,QACE,IAALnF,IAAWY,EAAI+J,EAAQ3K,IAAIwgB,aAAeD,GAAW3f,EAAEiK,SAAWA,OACvE7K,EAAI,eAIH,GAAIY,EAAE6f,WAAaF,GAAW3f,EAAE6f,YAAcF,SAIjDf,GAAoBS,IAA8BL,IACrD9J,GAAQrV,EAAIG,EAAEqM,OAAS3F,GAAUtD,GAAkB4V,IAAY,GAC/D9D,GAAQpV,EAAIE,EAAEuM,OAAS7F,GAAUzD,GAAiB+V,IAAY,GAC9D9T,GAAUA,EAAOc,MAAMkP,GAASA,IAChCwD,EAAWxD,GAAQrV,EACnB8Y,EAAWzD,GAAQpV,IACnB2b,EAAKpU,KAAK2a,IAAItJ,EAAW2E,OACzB3B,EAAKrU,KAAK2a,IAAIrJ,EAAW2E,MACDoE,EAALjG,GAA6BiG,EAALhG,IAA2BlJ,IAAc6M,IAA8BL,KACjHA,EAAsBtD,EAALD,GAAWrC,EAAU,IAAM,IACxCiG,GAA6BL,IAAkBK,GAClDrW,GAAa/H,GAAM,mBAAoBwI,KAEA,IAApC2O,EAAKzL,KAAKsV,uBAAmC7I,GAAUH,IAC1Db,EAAKV,WAAgC,MAAlBsH,EAAyB,IAAM,IAClDjY,EAAYqR,EAAKzL,KAAKuV,aAAe9J,EAAKzL,KAAKuV,WAAW/T,KAAKiK,EAAM2J,IAElEvP,IAAc6M,IAA8BL,IAC/CF,GAAUiD,IASZtD,EAJIrG,EAAKT,mBAAuB0H,KAA8BL,GAAiBK,IAA8BL,KAAgD,IAA7B+C,EAAcI,WAGpI1D,IACY,GAHtBhV,GAAgBsY,IACM,GAKnB3J,EAAKX,aACRa,IAAwB,GAEzBkB,GAAmBxZ,EAAEqM,MAAOrM,EAAEuM,MAAO+T,SAnDpC7B,GAAuBze,GAAKue,GAAW9U,GAAgBzJ,IAsDzDwZ,GAAqB,SAArBA,mBAAsBd,EAAUC,EAAUyJ,OAYxCC,EAASC,EAASziB,EAAGC,EAAG0Q,EAAK+R,EAX1BC,EAAgB,EAAIpK,EAAKf,eAC5BsD,EAAgB,EAAIvC,EAAKd,eACzBmL,EAAerK,EAAKM,SACpBgK,EAAetK,EAAKO,SACpBgK,EAAoB/E,EACpBgF,EAAQxK,EAAKvY,EACbgjB,EAAQzK,EAAKtY,EACbgjB,EAAW1K,EAAKyE,KAChBkG,EAAW3K,EAAK0E,KAChBkG,EAAkB5K,EAAK2E,YACvBkG,EAAY1K,EAEbH,EAAKM,SAAWA,EAChBN,EAAKO,SAAWA,EACZjS,KACHgS,GAAYtV,GAAkB4V,IAC9BL,GAAY1V,GAAiB+V,KAE1BS,GACH3Z,EAAIuH,KAAK6W,MAAMD,EAAene,EAAI6Y,EAAUD,EAAWuF,EAAepe,GAAK2U,GAEjE,KADVhE,EAAM4H,EAAKtY,EAAIA,IAEd8d,GAAiB,IACjBxF,EAAKtY,EAAIA,GACC0Q,GAAO,MACjBoN,GAAiB,IACjBxF,EAAKtY,EAAIA,GAITD,EAFGuY,EAAKvY,IAAMge,GAAiBxW,KAAKuD,IAAIvD,KAAK2a,IAAI3E,EAAgB3E,GAAWrR,KAAK2a,IAAI1E,EAAgB3E,IAAa+I,GAClHtJ,EAAKtY,EAAIA,EACL+d,GAAiBD,EAAgB9d,GAAK0iB,GAEtC3E,IAID3Y,IACHqd,EAAO7J,EAAWxT,EAAOtE,EAAI+X,EAAWzT,EAAOpE,EAAIoE,EAAOlF,EAC1D2Y,EAAWD,EAAWxT,EAAOrE,EAAI8X,EAAWzT,EAAOnE,EAAImE,EAAOlE,EAC9D0X,EAAW6J,IAEZD,EAAW3J,EAAW2E,GAERoE,IAA8BA,EAAXY,IAChCA,EAAU,IAFXD,EAAW3J,EAAW2E,GAIRqE,IAA8BA,EAAXW,IAChCA,EAAU,IAENjK,EAAKZ,UAAYY,EAAKV,cAAgB2K,GAAWC,MACrDC,EAAOnK,EAAKV,cAEXU,EAAKV,WAAa6K,EAAQnJ,GAAU/R,KAAK2a,IAAIK,GAAWhb,KAAK2a,IAAIM,GAAY,IAAMrJ,EAAS,IAAM,KAC9FsJ,GAAQxb,EAAYqR,EAAKzL,KAAKuV,aACjC9J,EAAKzL,KAAKuV,WAAW/T,KAAKiK,EAAMA,EAAKrL,eAG1B,MAATwV,EACHD,EAAU,EACS,MAATC,IACVF,EAAU,IAGZxiB,EAAIuH,GAAOyW,EAAgBwE,EAAUG,GACrC1iB,EAAIsH,GAAOwW,EAAgB0E,EAAUE,KAGjCnI,GAASC,GAASC,KAAYnC,EAAKvY,IAAMA,GAAMuY,EAAKtY,IAAMA,IAAM2Z,KAChEc,IACHtF,GAAOpV,EAAIA,EACXoV,GAAOnV,EAAIA,EACXyiB,EAAOhI,EAAOtF,IACdpV,EAAIuH,GAAOmb,EAAK1iB,GAChBC,EAAIsH,GAAOmb,EAAKziB,IAEbua,IACHxa,EAAIuH,GAAOiT,EAAMxa,KAEdya,IACHxa,EAAIsH,GAAOkT,EAAMxa,MAGfob,IACKzN,EAAJ5N,EACHA,EAAI4N,EAAOpG,KAAKC,OAAOzH,EAAI4N,GAAQkN,GACzB9a,EAAIyN,IACdzN,EAAIyN,EAAOjG,KAAKC,OAAOzH,EAAIyN,GAAQqN,IAE/BlB,IACI9L,EAAJ7N,EACHA,EAAIuH,KAAKC,MAAMqG,GAAQ7N,EAAI6N,GAAQgN,GACzB7a,EAAI0N,IACd1N,EAAIuH,KAAKC,MAAMkG,GAAQ1N,EAAI0N,GAAQmN,MAIlCvC,EAAKvY,IAAMA,IAAMuY,EAAKtY,IAAMA,GAAM2Z,KACjCA,GACHrB,EAAK2E,YAAc3E,EAAKvY,EAAIuY,EAAKyE,KAAOhd,EACxC0Y,GAAQ,IAEJU,IACHb,EAAKtY,EAAIsY,EAAK0E,KAAOhd,EACrByY,GAAQ,GAELa,IACHhB,EAAKvY,EAAIuY,EAAKyE,KAAOhd,EACrB0Y,GAAQ,IAGL6J,IAA2D,IAA3C5V,GAAe4L,EAAM,OAAQ,WAMjDA,EAAKM,SAAW+J,EAChBrK,EAAKO,SAAW+J,EAChB9E,EAAgB+E,EAChBvK,EAAKvY,EAAI+iB,EACTxK,EAAKtY,EAAI+iB,EACTzK,EAAKyE,KAAOiG,EACZ1K,EAAK0E,KAAOiG,EACZ3K,EAAK2E,YAAciG,EACnBzK,EAAQ0K,IAbH7K,EAAKC,YAAcD,EAAK3E,YAC5B2E,EAAKC,WAAasG,GAAU,EAC5BnS,GAAe4L,EAAM,YAAa,kBAiBtC0G,GAAY,SAAZA,UAAa9e,EAAGsQ,MACViO,GAAYnG,EAAK3E,aAAczT,GAAgB,MAAX2f,GAAoBrP,KAAWtQ,EAAE6f,WAAa7f,EAAE6f,YAAcF,GAAW3f,EAAEiK,SAAWA,GAAYjK,EAAEyf,iBAh0ClI,SAAdyD,YAAenN,EAAMoN,WAChB/jB,EAAI2W,EAAKxR,OACNnF,QACF2W,EAAK3W,GAAGwgB,aAAeuD,SACnB,EA4zCyJD,CAAYljB,EAAEyf,eAAgBE,MAI9LvH,EAAK3E,WAAY,MAKhB1J,EAAS3K,EAAGgkB,EAAgBC,EAAaC,EAJtCvB,EAAgB/hB,EACnBujB,EAAcnL,EAAKC,WACnBmL,EAAwBpL,EAAKzL,KAAK2S,kBAAoBtf,IAAMA,EAAEuf,SAAqB,EAAVvf,EAAEwf,OAC3EiE,EAAyB5c,GAAK6c,YAAY,KAAOrF,OAE9CO,GACHrV,GAAgBqV,EAAkB,WAAYE,WAC9CvV,GAAgBqV,EAAkB,YAAaG,IAC/CxV,GAAgBqV,EAAkB,cAAeE,WACjDvV,GAAgByP,GAAU,aAAc9O,KAExCX,GAAgByP,GAAU,YAAa+F,IAExCxV,GAAgBtI,GAAM,mBAAoBwI,IACrC0I,IAAqByM,IACzBrV,GAAgByP,GAAU,UAAW8F,WACrC9e,GAAKA,EAAEiK,QAAUV,GAAgBvJ,EAAEiK,OAAQ,UAAW6U,YAEvDvG,GAAQ,EACJgL,IACHzC,GAAc9L,GAAgBL,KAC9ByD,EAAKC,YAAa,GAEnB7P,GAAuB0P,IACnB+G,IAAeuE,SACdxjB,IACHuJ,GAAgBvJ,EAAEiK,OAAQ,SAAU6U,WACpC1G,EAAKrL,aAAegV,GAErBtT,GAAe2Q,GAAU,GACzB5S,GAAe4L,EAAM,UAAW,aAChC5L,GAAe4L,EAAM,QAAS,gBAC9B6G,GAAa,OAGd7f,EAAIggB,EAAS7a,QACC,IAALnF,GACRgM,GAAUgU,EAAShgB,GAAI,SAAUuN,EAAK4F,UAA2B,IAAhB5F,EAAK4F,OAAmBD,GAAiB,UAE3FtI,KACIhK,EAAG,KACN+J,EAAU/J,EAAEyf,kBAEXzf,EAAI+J,EAAQ,MACF2V,GAAS1f,EAAE4f,aAAeD,EAAS,KAC5CvgB,EAAI2K,EAAQxF,QACE,IAALnF,IAAWY,EAAI+J,EAAQ3K,IAAIwgB,aAAeD,GAAW3f,EAAEiK,SAAWA,OACvE7K,EAAI,IAAMkR,SAKhB8H,EAAKrL,aAAegV,EACpB3J,EAAKM,SAAW1Y,EAAEqM,MAClB+L,EAAKO,SAAW3Y,EAAEuM,aAEfiX,GAAwBzB,GAC3BtY,GAAgBsY,GAChBtD,GAAsB,EACtBjS,GAAe4L,EAAM,UAAW,cACtB2J,IAAkBwB,GAC5B9E,GAAsB,EAClBC,IAAgB/R,EAAKmB,MAAQnB,EAAKoO,SACrCgB,GAAQpP,EAAKmH,SAAWnH,EAAKgX,YAE9BnX,GAAe4L,EAAM,UAAW,aAC1B5F,IAAqC,cAAvBuP,EAAcniB,OAAmE,IAA1CmiB,EAAcniB,KAAK8S,QAAQ,YACrFlG,GAAe4L,EAAM,QAAS,WAC1BzD,KAAa6J,GAAY,KAC5BhS,GAAe4L,EAAM,cAAe,iBAErCiL,EAActB,EAAc9X,QAAUA,EACtCuU,GAAY7J,KACZ2O,EAAiB,0BACZ9E,KAAcwC,IAAiB5I,EAAKmG,WAAcnG,EAAK3E,WAAcsO,EAAcd,mBAClFoC,EAAYO,MACfP,EAAYO,QACF5K,GAAS6K,eACnBT,EAAiBpK,GAAS6K,YAAY,gBACvBC,eAAe,SAAS,GAAM,EAAM7iB,GAAM,EAAGmX,EAAKrL,aAAagX,QAAS3L,EAAKrL,aAAaiX,QAAS5L,EAAKM,SAAUN,EAAKO,UAAU,GAAO,GAAO,GAAO,EAAO,EAAG,MAC/K0K,EAAYrW,cAAcoW,MAIxB5Q,IAAeuP,EAAcd,kBACjCpa,GAAK6c,YAAY,IAAMJ,MAIzBvH,GAAQpP,EAAKmH,SAAWnH,EAAKgX,YACxBvL,EAAKT,oBAAqBoK,IAA0C,IAAxBpV,EAAKwS,gBAA6BD,GAAY/Q,KAAKiK,EAAM2J,EAAc9X,UAAYsZ,GAAiBlE,KAA8BL,GAAiBK,IAA8BL,KAAgD,IAA7B+C,EAAcI,WAIlQ1D,GAAsB,GAHtBA,GAAsB,EACtBhV,GAAgBsY,IAIjBvV,GAAe4L,EAAM,UAAW,cAEjC0F,MAAgB2F,EAAuB5a,SAAUuP,EAAK4D,MAAMnT,YAC5D0a,GAAe/W,GAAe4L,EAAM,UAAW,cACxC,EAxGNqG,GAAuBze,GAAKue,GAAW9U,GAAgBzJ,WA8J1DyhB,EAAMxL,UAAUvT,IAAIuH,KACbwX,EAAIzB,SAGNiE,UAAY,SAACva,EAAOwa,OACpBnN,EAAIC,EAAI7B,EAAIC,EAChBkJ,GAAQ5U,GAAS0O,EAAKrL,cAAc,GAEhCmX,IAAU9L,EAAK5B,QAAQ9M,GAAS0O,EAAKrL,gBACxCgK,EAAKrL,GAAWhC,GAAS0O,EAAKrL,cAC9BiK,EAAKtL,GAAWzB,GAChBkL,EAAKqM,GAAc,CAAC3hB,EAAEkX,EAAGzR,KAAOyR,EAAGnL,MAAQ,EAAG9L,EAAEiX,EAAG1R,IAAM0R,EAAG/K,OAAS,IACrEoJ,EAAKoM,GAAc,CAAC3hB,EAAEmX,EAAG1R,KAAO0R,EAAGpL,MAAQ,EAAG9L,EAAEkX,EAAG3R,IAAM2R,EAAGhL,OAAS,IACrEqR,GAAiBlI,EAAGtV,EAAIuV,EAAGvV,EAC3Byd,GAAiBnI,EAAGrV,EAAIsV,EAAGtV,GAEvBsY,EAAKC,aACTD,EAAKC,WAAasG,GAAU,EAC5BnS,GAAe4L,EAAM,YAAa,mBAG/BiI,KAAOtB,KACPrL,QAAU,SAAA1T,UAAK8e,GAAU9e,GAAKoY,EAAKrL,cAAc,MACjDwJ,cAAgB,kBAAM6B,EAAKC,WAAa,GAAK1D,KAAamM,IAAe,OACzEqD,eAAiB,kBAAOxP,KAAa6J,IAAa,OAClDhI,QAAU,SAACvM,EAAQ0M,UAAcV,UAAUO,QAAQ4B,EAAKnO,OAAQA,EAAQ0M,MAExEyN,aAAe,SAACC,EAAMC,OAEzBjC,EAASC,EAASiC,EAAOC,EAAWzN,EAAIC,EADrCyN,EAAiB,aAATJ,GAAuBzQ,GAAiByQ,EAAQpd,EAAUod,KAAU5K,EAAgB,UAAY,cAE/F,YAATgL,IACH1N,EAAKrL,GAAW0M,EAAKnO,QACrB+M,EAAKtL,GAAW2Y,IAEjBhC,EAAoB,UAAToC,EAAoBrM,EAAKvY,EAAIge,EAA0B,aAAT4G,EAAuB7Q,GAAc8Q,YAAYza,EAAQkR,GAAUpE,EAAGzR,KAAOyR,EAAGnL,MAAQ,GAAMoL,EAAG1R,KAAO0R,EAAGpL,MAAQ,GACxK6N,EACI4I,EAAU,EAAI,oBAAsB,aAE3CiC,EAAoBA,GAAqB,EACzChC,EAAoB,UAATmC,EAAoBrM,EAAKtY,EAAI8d,EAA0B,aAAT6G,EAAuB7Q,GAAc8Q,YAAYza,EAAQmR,GAAUrE,EAAG1R,IAAM0R,EAAG/K,OAAS,GAAMgL,EAAG3R,IAAM2R,EAAGhL,OAAS,GAE5KwY,GADAD,EAAQld,KAAK2a,IAAIK,EAAUC,IACN,EAAIgC,EAAqB,GAAMjC,EAAU,EAAK,OAAS,QACxEkC,EAAQD,IACO,KAAdE,IACHA,GAAa,KAEdA,GAAclC,EAAU,EAAK,KAAO,QAG/BkC,MAGHvH,YAAc,SAAC0H,EAAWC,OAC1B/kB,EAAGC,EAAGiO,EAAmB/N,EAAGoE,EAAQoU,KACpCmM,GAAahY,EAAKoO,SAAW4J,SAChChY,EAAKoO,OAAS4J,EACPvM,EAAKyM,QAAO,EAAMD,MAE1B5K,IAAO,GACPc,KACII,IAAc4C,KAAc,IAC/Bje,EAAIuY,EAAKvY,EACTC,EAAIsY,EAAKtY,EACD2N,EAAJ5N,EACHA,EAAI4N,EACM5N,EAAIyN,IACdzN,EAAIyN,GAEGK,EAAJ7N,EACHA,EAAI6N,EACM7N,EAAI0N,IACd1N,EAAI0N,IAED4K,EAAKvY,IAAMA,GAAKuY,EAAKtY,IAAMA,KAC9BiO,GAAoB,EACpBqK,EAAKvY,EAAIuY,EAAKyE,KAAOhd,EACjB4Z,EACHrB,EAAK2E,YAAcld,EAEnBuY,EAAKtY,EAAIsY,EAAK0E,KAAOhd,EAGtBoY,GADAK,GAAQ,GAEJH,EAAKX,aAAeW,EAAKC,gBAC5BxN,GAAkBZ,EAAO5K,YACzBW,EAAIiK,EACJc,GAAa5H,UAAkC,MAApBlC,GAAKiC,YAAuBjC,GAAKiC,YAAqD,MAAtC8V,GAASnX,gBAAgBsB,UAAqB6V,GAASnX,gBAAgBsB,UAAY6V,GAASlX,KAAKqB,UAC5K4H,GAAazH,WAAmC,MAApBrC,GAAKoC,YAAuBpC,GAAKoC,YAAsD,MAAvC2V,GAASnX,gBAAgByB,WAAsB0V,GAASnX,gBAAgByB,WAAa0V,GAASlX,KAAKwB,WACxKtD,IAAMwY,GAEZpU,GADAoU,EAASnO,GAAQrK,EAAEX,aACD0L,GAAe/K,EAAEX,WAC/B4Z,GAAU7U,EAAOjB,UAAYiB,EAAO6G,gBACvC7G,EAAOjB,UAAYiB,EAAO6G,eAEvBmO,GAAUhV,EAAOd,WAAac,EAAO4G,gBACxC5G,EAAOd,WAAac,EAAO4G,eAE5BhL,EAAIoE,EAIHgU,EAAKoC,aAAezM,GAAqBqK,EAAKyE,KAAOpP,GAAQ2K,EAAKyE,KAAOvP,GAAQ8K,EAAK0E,KAAOnP,GAAQyK,EAAK0E,KAAOtP,IACpHuO,GAAQpP,EAAKmH,SAAWnH,EAAKgX,WAAY5V,UAGpCqK,KAGHyM,OAAS,SAAC5H,EAAa2H,EAAQE,MAC/BF,GAAUxM,EAAK3E,UAAW,KACzB9S,EAAIwF,gBAAgB8D,GACvBtC,EAAIgW,EAAY3X,MAAM,CAACnG,EAAGuY,EAAKvY,EAAIge,EAAe/d,EAAGsY,EAAKtY,EAAI8d,IAC9DmH,EAAK5e,gBAAgB8D,EAAO5K,YAAY,GACzC0lB,EAAG/e,MAAM,CAACnG,EAAGc,EAAEX,EAAI2H,EAAE9H,EAAGC,EAAGa,EAAEK,EAAI2G,EAAE7H,GAAI6H,GACvCyQ,EAAKvY,GAAK8H,EAAE9H,EAAIklB,EAAG/kB,EACnBoY,EAAKtY,GAAK6H,EAAE7H,EAAIilB,EAAG/jB,EACnBkX,IAAO,GACPqF,SAEK1d,EAASuY,EAATvY,EAAGC,EAAMsY,EAANtY,SACTod,IAAc0H,GACV3H,EACH7E,EAAK6E,eAEL1E,GAASuM,GAAyB5M,IAAO,GACzC8B,IAAO,IAEJ4K,IACHpL,GAAmBpB,EAAKM,SAAUN,EAAKO,UACvCJ,GAASL,IAAO,IAEbE,EAAK3E,YAAcmR,IAAYxL,GAAiC,IAAvB/R,KAAK2a,IAAIniB,EAAIuY,EAAKvY,IAAeoZ,GAAkC,IAAvB5R,KAAK2a,IAAIliB,EAAIsY,EAAKtY,KAAc2Z,IACxH8D,KAEGnF,EAAKX,aACR5M,GAAkBZ,EAAO5K,WAAY+Y,EAAKC,YAC1CC,GAAwBF,EAAKC,WAC7BH,IAAO,GAEP5N,GAAsBL,EAAQwW,IAC9BtW,GAAmBF,EAAQwW,KAErBrI,KAGHlH,OAAS,SAAAtR,OAEZolB,EAAI5lB,EAAGuiB,EADJsD,EAAU,CAACnW,MAAM,OAED,IAAhBnC,EAAK4F,SACR0S,EAAQ1S,OAAS5F,EAAK4F,QAAUD,IAE7BzL,GAAKoI,MAAMgF,YAAY,kBAC1BgR,EAAQC,aAAe,QAEX,SAATtlB,EAAiB,KACpBiI,GAAiCuX,EAAWhG,GAAWH,EAAU,OAAUtM,EAAK0S,2BAA8BpV,EAAO+F,eAAiB/F,EAAOkC,eAAmBlC,EAAO8F,cAAgB9F,EAAOkC,eAAkBQ,EAAKgL,kBAAoB,eAAiByB,EAAS,QAAU,SAC7Qha,EAAIggB,EAAS7a,QACC,IAALnF,GACRuiB,EAAUvC,EAAShgB,GACnB+S,IAAoBnJ,GAAa2Y,EAAS,YAAarD,IACvDtV,GAAa2Y,EAAS,aAAcrD,IACpCtV,GAAa2Y,EAAS,QAASjB,IAAS,GACxC7Z,GAAKgI,IAAI8S,EAASsD,GACdtD,EAAQ/c,SAAW+c,EAAQpe,iBAAmB6V,GAAWH,GAC5DpS,GAAKgI,IAAI8S,EAAQpe,gBAAiB,CAACyE,YAAa2E,EAAK0S,2BAA6B1S,EAAKgL,kBAAoB,eAAiByB,EAAS,QAAU,UAEhJzM,EAAK2S,kBAAoBtW,GAAa2Y,EAAS,cAAe3J,IAE/DvJ,GAAe2Q,GAAU,UAE1BjV,GAAmBF,EAAQwW,IAC3BlC,GAAU,EACN3K,IAA0B,SAAThU,GACpBgU,GAAcuR,MAAMxL,GAAe1P,EAAS4P,EAAS,MAAQJ,EAAe,WAAa,YAE1FxP,EAAOqM,UAAY0O,EAAK/a,EAAOqM,WAAc,IAAOvB,KACpDvB,GAAQwR,GAAM5M,EACVuB,IACHA,EAAYzI,SACZyI,EAAYxa,QAAQmX,UAAY0O,IAEhCrY,EAAKoO,QAAUtB,IAAiB8D,KACjC5Q,EAAKoO,QAAU3C,EAAK6E,cACb7E,KAGHpH,QAAU,SAAApR,WAGb+hB,EAFGyD,EAAWhN,EAAKC,WACnBjZ,EAAIggB,EAAS7a,QAEA,IAALnF,GACRgM,GAAUgU,EAAShgB,GAAI,SAAU,SAErB,SAATQ,EAAiB,KACpBiI,GAAiCuX,EAAU,MAC3ChgB,EAAIggB,EAAS7a,QACC,IAALnF,GACRuiB,EAAUvC,EAAShgB,GACnBgM,GAAUuW,EAAS,eAAgB,MACnCpY,GAAgBoY,EAAS,YAAarD,IACtC/U,GAAgBoY,EAAS,aAAcrD,IACvC/U,GAAgBoY,EAAS,QAASjB,IAAS,GAC3CnX,GAAgBoY,EAAS,cAAe3J,IAEzCvJ,GAAe2Q,GAAU,GACrBR,IACHrV,GAAgBqV,EAAkB,cAAeE,IACjDvV,GAAgBqV,EAAkB,WAAYE,IAC9CvV,GAAgBqV,EAAkB,YAAaG,KAEhDxV,GAAgByP,GAAU,UAAW8F,IACrCvV,GAAgByP,GAAU,YAAa+F,WAExCzU,GAAsBL,EAAQwW,IAC9BlC,GAAU,EACN3K,IAA0B,SAAThU,IACpBgU,GAAcyR,QAAQ1L,GAAe1P,EAAS4P,EAAS,MAAQJ,EAAe,WAAa,YAC3FrB,EAAK4D,OAAS5D,EAAK4D,MAAMgE,QAE1BrG,GAAeA,EAAY3I,UAC3BxI,GAAuB0P,IACvBE,EAAKC,WAAaD,EAAK3E,UAAYwL,GAAa,EAChDmG,GAAY5Y,GAAe4L,EAAM,UAAW,aACrCA,KAGHmG,QAAU,SAASvX,EAAOpH,UACvB2Q,UAAUhM,OAAUyC,EAAQoR,EAAKlH,OAAOtR,GAAQwY,EAAKpH,QAAQpR,GAAS2e,KAGzEyB,KAAO,kBACX5H,EAAKoC,YAAa,EAClBpC,EAAK4D,OAAS5D,EAAK4D,MAAMgE,OACzB5H,EAAKpH,UACLnK,GAAKgI,IAAIuQ,EAAU,CAACkG,WAAW,sBACxB9R,GAAQvJ,EAAOqM,WACf8B,KAGHmN,OAAS,gBACRvF,YACA9I,QAAU1R,KAAK0R,OAAOqO,WAGvB3lB,EAAK8S,QAAQ,YACjBiH,EAAcxC,EAAKwC,YAAc,IAAI3K,GAAY/E,EAjxDzC,SAAVub,QAAW/d,EAAKge,OACV,IAAI9d,KAAK8d,EACP9d,KAAKF,IACVA,EAAIE,GAAK8d,EAAS9d,WAGbF,EA2wDmD+d,CAAQ,CAAC7U,OAAO,kBACvEyH,EAAK3E,WAAaqL,GAAU,QAC1BnS,IAEJ1C,EAAOzJ,MAAMklB,UAAazM,IAAWtG,GAAkB,OAAS,SAChE1I,EAAOzJ,MAAMmlB,UAAavM,IAAWzG,GAAkB,OAAS,SAChE1I,EAAS0P,EAAYlK,SAGlBgK,EACHwG,EAAU7I,SAAW,GAEjBgC,IACH6G,EAAU9E,GAAS,GAEhBlC,IACHgH,EAAU7E,GAAS,IAIrBrD,GAAQ1H,UAAW,YAAa1D,IAAQA,EAAK0D,QAE7C0D,gCACK7C,YAtwDS,SAAf0U,aAAgBne,EAAKge,OACf,IAAI9d,KAAK8d,EACP9d,KAAKF,IACVA,EAAIE,GAAK8d,EAAS9d,IAyzDtBie,CAAa3P,EAAU4P,UAAW,CAACnN,SAAS,EAAGC,SAAU,EAAGwF,OAAQ,EAAGC,OAAQ,EAAG1E,OAAQ,EAAGE,OAAQ,EAAGvB,YAAY,EAAO5E,WAAW,IAEtIwC,EAAUkK,OAAS,IACnBlK,EAAU6P,QAAU,SAEpBlf,KAAcC,GAAKC,eAAemP"}