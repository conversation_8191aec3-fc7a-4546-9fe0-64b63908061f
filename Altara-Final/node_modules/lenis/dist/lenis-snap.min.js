function t(e){"sticky"===getComputedStyle(e).position&&(e.style.setProperty("position","static"),e.dataset.sticky="true"),e.offsetParent&&t(e.offsetParent)}function e(t){"true"===t?.dataset?.sticky&&(t.style.removeProperty("position"),delete t.dataset.sticky),t.offsetParent&&e(t.offsetParent)}function i(t,e=0){const s=e+t.offsetTop;return t.offsetParent?i(t.offsetParent,s):s}function s(t,e=0){const i=e+t.offsetLeft;return t.offsetParent?s(t.offsetParent,i):i}function o(t,e=0){const i=e+t.scrollTop;return t.offsetParent?o(t.offsetParent,i):i+window.scrollY}function n(t,e=0){const i=e+t.scrollLeft;return t.offsetParent?n(t.offsetParent,i):i+window.scrollX}var r=class{element;options;align;rect={};wrapperResizeObserver;resizeObserver;constructor(t,{align:e=["start"],ignoreSticky:i=!0,ignoreTransform:s=!1}={}){this.element=t,this.options={align:e,ignoreSticky:i,ignoreTransform:s},this.align=[e].flat(),this.wrapperResizeObserver=new ResizeObserver(this.onWrapperResize),this.wrapperResizeObserver.observe(document.body),this.onWrapperResize(),this.resizeObserver=new ResizeObserver(this.onResize),this.resizeObserver.observe(this.element),this.setRect({width:this.element.offsetWidth,height:this.element.offsetHeight})}destroy(){this.wrapperResizeObserver.disconnect(),this.resizeObserver.disconnect()}setRect({top:t,left:e,width:i,height:s,element:o}={}){t=t??this.rect.top,e=e??this.rect.left,i=i??this.rect.width,s=s??this.rect.height,o=o??this.rect.element,t===this.rect.top&&e===this.rect.left&&i===this.rect.width&&s===this.rect.height&&o===this.rect.element||(this.rect.top=t,this.rect.y=t,this.rect.width=i,this.rect.height=s,this.rect.left=e,this.rect.x=e,this.rect.bottom=t+s,this.rect.right=e+i)}onWrapperResize=()=>{let r,h;if(this.options.ignoreSticky&&t(this.element),this.options.ignoreTransform)r=i(this.element),h=s(this.element);else{const t=this.element.getBoundingClientRect();r=t.top+o(this.element),h=t.left+n(this.element)}this.options.ignoreSticky&&e(this.element),this.setRect({top:r,left:h})};onResize=([t])=>{if(!t?.borderBoxSize[0])return;const e=t.borderBoxSize[0].inlineSize,i=t.borderBoxSize[0].blockSize;this.setRect({width:e,height:i})}},h=0;function a(){return h++}globalThis.Snap=class{constructor(t,{type:e="mandatory",lerp:i,easing:s,duration:o,velocityThreshold:n=1,debounce:r=0,onSnapStart:h,onSnapComplete:a}={}){this.lenis=t,this.options={type:e,lerp:i,easing:s,duration:o,velocityThreshold:n,debounce:r,onSnapStart:h,onSnapComplete:a},this.onWindowResize(),window.addEventListener("resize",this.onWindowResize,!1),this.onSnapDebounced=function(t,e){let i;return function(...s){let o=this;clearTimeout(i),i=setTimeout((()=>{i=void 0,t.apply(o,s)}),e)}}(this.onSnap,this.options.debounce),this.lenis.on("scroll",this.onScroll)}options;elements=new Map;snaps=new Map;viewport={width:window.innerWidth,height:window.innerHeight};isStopped=!1;onSnapDebounced;destroy(){this.lenis.off("scroll",this.onScroll),window.removeEventListener("resize",this.onWindowResize,!1),this.elements.forEach((t=>t.destroy()))}start(){this.isStopped=!1}stop(){this.isStopped=!0}add(t,e={}){const i=a();return this.snaps.set(i,{value:t,userData:e}),()=>this.remove(i)}remove(t){this.snaps.delete(t)}addElement(t,e={}){const i=a();return this.elements.set(i,new r(t,e)),()=>this.removeElement(i)}removeElement(t){this.elements.delete(t)}onWindowResize=()=>{this.viewport.width=window.innerWidth,this.viewport.height=window.innerHeight};onScroll=({lastVelocity:t,velocity:e,userData:i})=>{if(this.isStopped)return;const s=Math.abs(t)>Math.abs(e),o=Math.sign(t)!==Math.sign(e)&&0!==e;Math.abs(e)<this.options.velocityThreshold&&s&&!o&&"snap"!==i?.initiator&&this.onSnapDebounced()};onSnap=()=>{let{scroll:t,isHorizontal:e}=this.lenis;t=Math.ceil(this.lenis.scroll);let i=[...this.snaps.values()];this.elements.forEach((({rect:t,align:s})=>{let o;s.forEach((s=>{"start"===s?o=t.top:"center"===s?o=e?t.left+t.width/2-this.viewport.width/2:t.top+t.height/2-this.viewport.height/2:"end"===s&&(o=e?t.left+t.width-this.viewport.width:t.top+t.height-this.viewport.height),"number"==typeof o&&i.push({value:Math.ceil(o),userData:{}})}))})),i=i.sort(((t,e)=>Math.abs(t.value)-Math.abs(e.value)));let s=i.findLast((({value:e})=>e<=t));void 0===s&&(s=i[0]);const o=Math.abs(t-s.value);let n=i.find((({value:e})=>e>=t));void 0===n&&(n=i[i.length-1]);const r=o<Math.abs(t-n.value)?s:n,h=Math.abs(t-r.value);("mandatory"===this.options.type||"proximity"===this.options.type&&h<=(e?this.lenis.dimensions.width:this.lenis.dimensions.height))&&this.lenis.scrollTo(r.value,{lerp:this.options.lerp,easing:this.options.easing,duration:this.options.duration,userData:{initiator:"snap"},onStart:()=>{this.options.onSnapStart?.(r)},onComplete:()=>{this.options.onSnapComplete?.(r)}})}};//# sourceMappingURL=lenis-snap.min.js.map