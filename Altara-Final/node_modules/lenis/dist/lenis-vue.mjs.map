{"version": 3, "sources": ["../packages/vue/src/provider.ts", "../packages/vue/src/store.ts", "../packages/vue/src/use-lenis.ts"], "sourcesContent": ["// import Tempus from '@darkroom.engineering/tempus'\nimport Lenis, { type ScrollCallback } from 'lenis'\nimport type {\n  HTMLAttributes,\n  InjectionKey,\n  Plugin,\n  PropType,\n  ShallowRef,\n} from 'vue'\nimport {\n  defineComponent,\n  h,\n  onWatcherCleanup,\n  provide,\n  reactive,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { globalAddCallback, globalLenis, globalRemoveCallback } from './store'\n\nexport const LenisSymbol: InjectionKey<ShallowRef<Lenis | undefined>> =\n  Symbol('LenisContext')\nexport const AddCallbackSymbol: InjectionKey<\n  (callback: any, priority: number) => void\n> = Symbol('AddCallback')\nexport const RemoveCallbackSymbol: InjectionKey<(callback: any) => void> =\n  Symbol('RemoveCallback')\n\nexport const VueLenis = defineComponent({\n  name: 'VueLenis',\n  props: {\n    root: {\n      type: Boolean as PropType<boolean>,\n      default: false,\n    },\n    autoRaf: {\n      type: Boolean as PropType<boolean>,\n      default: true,\n    },\n    options: {\n      type: Object as PropType<ConstructorParameters<typeof Lenis>[0]>,\n      default: () => ({}),\n    },\n    props: {\n      type: Object as PropType<HTMLAttributes>,\n      default: () => ({}),\n    },\n  },\n  setup(props, { slots, expose }) {\n    const lenisRef = shallowRef<Lenis>()\n    // const tempusCleanupRef = shallowRef<() => void>()\n    const wrapper = ref<HTMLDivElement>()\n    const content = ref<HTMLDivElement>()\n    // Setup exposed properties\n    expose({\n      lenis: lenisRef,\n      wrapper,\n      content,\n    })\n\n    // Sync options\n    watch(\n      [() => props.options, wrapper, content],\n      () => {\n        const isClient = typeof window !== 'undefined'\n\n        if (!isClient) return\n\n        if (!props.root && (!wrapper.value || !content.value)) return\n\n        lenisRef.value = new Lenis({\n          ...props.options,\n          ...(!props.root\n            ? {\n                wrapper: wrapper.value,\n                content: content.value,\n              }\n            : {}),\n          autoRaf: props.options?.autoRaf ?? props.autoRaf,\n        })\n\n        onWatcherCleanup(() => {\n          lenisRef.value?.destroy()\n          lenisRef.value = undefined\n        })\n      },\n      { deep: true, immediate: true }\n    )\n\n    const callbacks = reactive<\n      { callback: ScrollCallback; priority: number }[]\n    >([])\n\n    function addCallback(callback: ScrollCallback, priority: number) {\n      callbacks.push({ callback, priority })\n      callbacks.sort((a, b) => a.priority - b.priority)\n    }\n\n    function removeCallback(callback: ScrollCallback) {\n      callbacks.splice(\n        callbacks.findIndex((cb) => cb.callback === callback),\n        1\n      )\n    }\n\n    const onScroll: ScrollCallback = (data) => {\n      for (let i = 0; i < callbacks.length; i++) {\n        callbacks[i]?.callback(data)\n      }\n    }\n\n    watch(\n      [lenisRef, () => props.root],\n      ([lenis, root]) => {\n        lenis?.on('scroll', onScroll)\n\n        if (root) {\n          globalLenis.value = lenis\n          globalAddCallback.value = addCallback\n          globalRemoveCallback.value = removeCallback\n\n          onWatcherCleanup(() => {\n            globalLenis.value = undefined\n            globalAddCallback.value = undefined\n            globalRemoveCallback.value = undefined\n          })\n        }\n      },\n      { immediate: true }\n    )\n\n    if (!props.root) {\n      provide(LenisSymbol, lenisRef)\n      provide(AddCallbackSymbol, addCallback)\n      provide(RemoveCallbackSymbol, removeCallback)\n    }\n\n    return () => {\n      if (props.root) {\n        return slots.default?.()\n      } else {\n        return h('div', { ref: wrapper, ...props?.props }, [\n          h('div', { ref: content }, slots.default?.()),\n        ])\n      }\n    }\n  },\n})\n\nexport const vueLenisPlugin: Plugin = (app) => {\n  app.component('vue-lenis', VueLenis)\n  // Setup a global provide to silence top level useLenis injection warning\n  app.provide(LenisSymbol, shallowRef(undefined))\n  app.provide(AddCallbackSymbol, undefined as any)\n  app.provide(RemoveCallbackSymbol, undefined as any)\n}\n\n// @ts-ignore\ndeclare module '@vue/runtime-core' {\n  export interface GlobalComponents {\n    'vue-lenis': typeof VueLenis\n  }\n}\n", "import type Lenis from 'lenis'\nimport type { ScrollCallback } from 'lenis'\nimport { shallowRef } from 'vue'\n\nexport const globalLenis = shallowRef<Lenis>()\nexport const globalAddCallback =\n  shallowRef<(callback: ScrollCallback, priority: number) => void>()\nexport const globalRemoveCallback =\n  shallowRef<(callback: ScrollCallback) => void>()\n", "import type { Sc<PERSON><PERSON>allback } from 'lenis'\nimport { computed, inject, nextTick, onWatcherCleanup, watch } from 'vue'\nimport {\n  AddCallbackSymbol,\n  LenisSymbol,\n  RemoveCallbackSymbol,\n} from './provider'\nimport { globalAddCallback, globalLenis, globalRemoveCallback } from './store'\n\nexport function useLenis(callback?: ScrollCallback, priority = 0) {\n  const lenisInjection = inject(LenisSymbol)\n  const addCallbackInjection = inject(AddCallbackSymbol)\n  const removeCallbackInjection = inject(RemoveCallbackSymbol)\n\n  const addCallback = computed(() =>\n    addCallbackInjection ? addCallbackInjection : globalAddCallback.value\n  )\n  const removeCallback = computed(() =>\n    removeCallbackInjection\n      ? removeCallbackInjection\n      : globalRemoveCallback.value\n  )\n\n  const lenis = computed(() =>\n    lenisInjection?.value ? lenisInjection.value : globalLenis.value\n  )\n\n  if (typeof window !== 'undefined') {\n    // Wait two ticks to make sure the lenis instance is mounted\n    nextTick(() => {\n      nextTick(() => {\n        if (!lenis.value) {\n          console.warn(\n            'No lenis instance found, either mount a root lenis instance or wrap your component in a lenis provider'\n          )\n        }\n      })\n    })\n  }\n\n  watch(\n    [lenis, addCallback, removeCallback],\n    ([lenis, addCallback, removeCallback]) => {\n      if (!lenis || !addCallback || !removeCallback || !callback) return\n\n      addCallback?.(callback, priority)\n      callback?.(lenis as any)\n\n      onWatcherCleanup(() => {\n        removeCallback?.(callback)\n      })\n    },\n    {\n      immediate: true,\n    }\n  )\n  return lenis\n}\n"], "mappings": ";AACA,OAAO,WAAoC;AAQ3C;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAAA;AAAA,EACA;AAAA,OACK;;;AChBP,SAAS,kBAAkB;AAEpB,IAAM,cAAc,WAAkB;AACtC,IAAM,oBACX,WAAiE;AAC5D,IAAM,uBACX,WAA+C;;;ADa1C,IAAM,cACX,OAAO,cAAc;AAChB,IAAM,oBAET,OAAO,aAAa;AACjB,IAAM,uBACX,OAAO,gBAAgB;AAElB,IAAM,WAAW,gBAAgB;AAAA,EACtC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,MAAM,OAAO,EAAE,OAAO,OAAO,GAAG;AAC9B,UAAM,WAAWC,YAAkB;AAEnC,UAAM,UAAU,IAAoB;AACpC,UAAM,UAAU,IAAoB;AAEpC,WAAO;AAAA,MACL,OAAO;AAAA,MACP;AAAA,MACA;AAAA,IACF,CAAC;AAGD;AAAA,MACE,CAAC,MAAM,MAAM,SAAS,SAAS,OAAO;AAAA,MACtC,MAAM;AACJ,cAAM,WAAW,OAAO,WAAW;AAEnC,YAAI,CAAC,SAAU;AAEf,YAAI,CAAC,MAAM,SAAS,CAAC,QAAQ,SAAS,CAAC,QAAQ,OAAQ;AAEvD,iBAAS,QAAQ,IAAI,MAAM;AAAA,UACzB,GAAG,MAAM;AAAA,UACT,GAAI,CAAC,MAAM,OACP;AAAA,YACE,SAAS,QAAQ;AAAA,YACjB,SAAS,QAAQ;AAAA,UACnB,IACA,CAAC;AAAA,UACL,SAAS,MAAM,SAAS,WAAW,MAAM;AAAA,QAC3C,CAAC;AAED,yBAAiB,MAAM;AACrB,mBAAS,OAAO,QAAQ;AACxB,mBAAS,QAAQ;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,MACA,EAAE,MAAM,MAAM,WAAW,KAAK;AAAA,IAChC;AAEA,UAAM,YAAY,SAEhB,CAAC,CAAC;AAEJ,aAAS,YAAY,UAA0B,UAAkB;AAC/D,gBAAU,KAAK,EAAE,UAAU,SAAS,CAAC;AACrC,gBAAU,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ;AAAA,IAClD;AAEA,aAAS,eAAe,UAA0B;AAChD,gBAAU;AAAA,QACR,UAAU,UAAU,CAAC,OAAO,GAAG,aAAa,QAAQ;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAEA,UAAM,WAA2B,CAAC,SAAS;AACzC,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAU,CAAC,GAAG,SAAS,IAAI;AAAA,MAC7B;AAAA,IACF;AAEA;AAAA,MACE,CAAC,UAAU,MAAM,MAAM,IAAI;AAAA,MAC3B,CAAC,CAAC,OAAO,IAAI,MAAM;AACjB,eAAO,GAAG,UAAU,QAAQ;AAE5B,YAAI,MAAM;AACR,sBAAY,QAAQ;AACpB,4BAAkB,QAAQ;AAC1B,+BAAqB,QAAQ;AAE7B,2BAAiB,MAAM;AACrB,wBAAY,QAAQ;AACpB,8BAAkB,QAAQ;AAC1B,iCAAqB,QAAQ;AAAA,UAC/B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IACpB;AAEA,QAAI,CAAC,MAAM,MAAM;AACf,cAAQ,aAAa,QAAQ;AAC7B,cAAQ,mBAAmB,WAAW;AACtC,cAAQ,sBAAsB,cAAc;AAAA,IAC9C;AAEA,WAAO,MAAM;AACX,UAAI,MAAM,MAAM;AACd,eAAO,MAAM,UAAU;AAAA,MACzB,OAAO;AACL,eAAO,EAAE,OAAO,EAAE,KAAK,SAAS,GAAG,OAAO,MAAM,GAAG;AAAA,UACjD,EAAE,OAAO,EAAE,KAAK,QAAQ,GAAG,MAAM,UAAU,CAAC;AAAA,QAC9C,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAEM,IAAM,iBAAyB,CAAC,QAAQ;AAC7C,MAAI,UAAU,aAAa,QAAQ;AAEnC,MAAI,QAAQ,aAAaA,YAAW,MAAS,CAAC;AAC9C,MAAI,QAAQ,mBAAmB,MAAgB;AAC/C,MAAI,QAAQ,sBAAsB,MAAgB;AACpD;;;AE3JA,SAAS,UAAU,QAAQ,UAAU,oBAAAC,mBAAkB,SAAAC,cAAa;AAQ7D,SAAS,SAAS,UAA2B,WAAW,GAAG;AAChE,QAAM,iBAAiB,OAAO,WAAW;AACzC,QAAM,uBAAuB,OAAO,iBAAiB;AACrD,QAAM,0BAA0B,OAAO,oBAAoB;AAE3D,QAAM,cAAc;AAAA,IAAS,MAC3B,uBAAuB,uBAAuB,kBAAkB;AAAA,EAClE;AACA,QAAM,iBAAiB;AAAA,IAAS,MAC9B,0BACI,0BACA,qBAAqB;AAAA,EAC3B;AAEA,QAAM,QAAQ;AAAA,IAAS,MACrB,gBAAgB,QAAQ,eAAe,QAAQ,YAAY;AAAA,EAC7D;AAEA,MAAI,OAAO,WAAW,aAAa;AAEjC,aAAS,MAAM;AACb,eAAS,MAAM;AACb,YAAI,CAAC,MAAM,OAAO;AAChB,kBAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,EAAAC;AAAA,IACE,CAAC,OAAO,aAAa,cAAc;AAAA,IACnC,CAAC,CAACC,QAAOC,cAAaC,eAAc,MAAM;AACxC,UAAI,CAACF,UAAS,CAACC,gBAAe,CAACC,mBAAkB,CAAC,SAAU;AAE5D,MAAAD,eAAc,UAAU,QAAQ;AAChC,iBAAWD,MAAY;AAEvB,MAAAG,kBAAiB,MAAM;AACrB,QAAAD,kBAAiB,QAAQ;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO;AACT;", "names": ["shallowRef", "shallowRef", "onWatcherCleanup", "watch", "watch", "lenis", "addCallback", "removeCallback", "onWatcherCleanup"]}