{"version": 3, "sources": ["../../gsap/ScrollToPlugin.js"], "sourcesContent": ["/*!\n * ScrollToPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar gsap,\n    _coreInitted,\n    _window,\n    _docEl,\n    _body,\n    _toArray,\n    _config,\n    ScrollTrigger,\n    _windowExists = function _windowExists() {\n  return typeof window !== \"undefined\";\n},\n    _getGSAP = function _getGSAP() {\n  return gsap || _windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n},\n    _isString = function _isString(value) {\n  return typeof value === \"string\";\n},\n    _isFunction = function _isFunction(value) {\n  return typeof value === \"function\";\n},\n    _max = function _max(element, axis) {\n  var dim = axis === \"x\" ? \"Width\" : \"Height\",\n      scroll = \"scroll\" + dim,\n      client = \"client\" + dim;\n  return element === _window || element === _docEl || element === _body ? Math.max(_docEl[scroll], _body[scroll]) - (_window[\"inner\" + dim] || _docEl[client] || _body[client]) : element[scroll] - element[\"offset\" + dim];\n},\n    _buildGetter = function _buildGetter(e, axis) {\n  //pass in an element and an axis (\"x\" or \"y\") and it'll return a getter function for the scroll position of that element (like scrollTop or scrollLeft, although if the element is the window, it'll use the pageXOffset/pageYOffset or the documentElement's scrollTop/scrollLeft or document.body's. Basically this streamlines things and makes a very fast getter across browsers.\n  var p = \"scroll\" + (axis === \"x\" ? \"Left\" : \"Top\");\n\n  if (e === _window) {\n    if (e.pageXOffset != null) {\n      p = \"page\" + axis.toUpperCase() + \"Offset\";\n    } else {\n      e = _docEl[p] != null ? _docEl : _body;\n    }\n  }\n\n  return function () {\n    return e[p];\n  };\n},\n    _clean = function _clean(value, index, target, targets) {\n  _isFunction(value) && (value = value(index, target, targets));\n\n  if (typeof value !== \"object\") {\n    return _isString(value) && value !== \"max\" && value.charAt(1) !== \"=\" ? {\n      x: value,\n      y: value\n    } : {\n      y: value\n    }; //if we don't receive an object as the parameter, assume the user intends \"y\".\n  } else if (value.nodeType) {\n    return {\n      y: value,\n      x: value\n    };\n  } else {\n    var result = {},\n        p;\n\n    for (p in value) {\n      result[p] = p !== \"onAutoKill\" && _isFunction(value[p]) ? value[p](index, target, targets) : value[p];\n    }\n\n    return result;\n  }\n},\n    _getOffset = function _getOffset(element, container) {\n  element = _toArray(element)[0];\n\n  if (!element || !element.getBoundingClientRect) {\n    return console.warn(\"scrollTo target doesn't exist. Using 0\") || {\n      x: 0,\n      y: 0\n    };\n  }\n\n  var rect = element.getBoundingClientRect(),\n      isRoot = !container || container === _window || container === _body,\n      cRect = isRoot ? {\n    top: _docEl.clientTop - (_window.pageYOffset || _docEl.scrollTop || _body.scrollTop || 0),\n    left: _docEl.clientLeft - (_window.pageXOffset || _docEl.scrollLeft || _body.scrollLeft || 0)\n  } : container.getBoundingClientRect(),\n      offsets = {\n    x: rect.left - cRect.left,\n    y: rect.top - cRect.top\n  };\n\n  if (!isRoot && container) {\n    //only add the current scroll position if it's not the window/body.\n    offsets.x += _buildGetter(container, \"x\")();\n    offsets.y += _buildGetter(container, \"y\")();\n  }\n\n  return offsets;\n},\n    _parseVal = function _parseVal(value, target, axis, currentVal, offset) {\n  return !isNaN(value) && typeof value !== \"object\" ? parseFloat(value) - offset : _isString(value) && value.charAt(1) === \"=\" ? parseFloat(value.substr(2)) * (value.charAt(0) === \"-\" ? -1 : 1) + currentVal - offset : value === \"max\" ? _max(target, axis) - offset : Math.min(_max(target, axis), _getOffset(value, target)[axis] - offset);\n},\n    _initCore = function _initCore() {\n  gsap = _getGSAP();\n\n  if (_windowExists() && gsap && typeof document !== \"undefined\" && document.body) {\n    _window = window;\n    _body = document.body;\n    _docEl = document.documentElement;\n    _toArray = gsap.utils.toArray;\n    gsap.config({\n      autoKillThreshold: 7\n    });\n    _config = gsap.config();\n    _coreInitted = 1;\n  }\n};\n\nexport var ScrollToPlugin = {\n  version: \"3.13.0\",\n  name: \"scrollTo\",\n  rawVars: 1,\n  register: function register(core) {\n    gsap = core;\n\n    _initCore();\n  },\n  init: function init(target, value, tween, index, targets) {\n    _coreInitted || _initCore();\n    var data = this,\n        snapType = gsap.getProperty(target, \"scrollSnapType\");\n    data.isWin = target === _window;\n    data.target = target;\n    data.tween = tween;\n    value = _clean(value, index, target, targets);\n    data.vars = value;\n    data.autoKill = !!(\"autoKill\" in value ? value : _config).autoKill;\n    data.getX = _buildGetter(target, \"x\");\n    data.getY = _buildGetter(target, \"y\");\n    data.x = data.xPrev = data.getX();\n    data.y = data.yPrev = data.getY();\n    ScrollTrigger || (ScrollTrigger = gsap.core.globals().ScrollTrigger);\n    gsap.getProperty(target, \"scrollBehavior\") === \"smooth\" && gsap.set(target, {\n      scrollBehavior: \"auto\"\n    });\n\n    if (snapType && snapType !== \"none\") {\n      // disable scroll snapping to avoid strange behavior\n      data.snap = 1;\n      data.snapInline = target.style.scrollSnapType;\n      target.style.scrollSnapType = \"none\";\n    }\n\n    if (value.x != null) {\n      data.add(data, \"x\", data.x, _parseVal(value.x, target, \"x\", data.x, value.offsetX || 0), index, targets);\n\n      data._props.push(\"scrollTo_x\");\n    } else {\n      data.skipX = 1;\n    }\n\n    if (value.y != null) {\n      data.add(data, \"y\", data.y, _parseVal(value.y, target, \"y\", data.y, value.offsetY || 0), index, targets);\n\n      data._props.push(\"scrollTo_y\");\n    } else {\n      data.skipY = 1;\n    }\n  },\n  render: function render(ratio, data) {\n    var pt = data._pt,\n        target = data.target,\n        tween = data.tween,\n        autoKill = data.autoKill,\n        xPrev = data.xPrev,\n        yPrev = data.yPrev,\n        isWin = data.isWin,\n        snap = data.snap,\n        snapInline = data.snapInline,\n        x,\n        y,\n        yDif,\n        xDif,\n        threshold;\n\n    while (pt) {\n      pt.r(ratio, pt.d);\n      pt = pt._next;\n    }\n\n    x = isWin || !data.skipX ? data.getX() : xPrev;\n    y = isWin || !data.skipY ? data.getY() : yPrev;\n    yDif = y - yPrev;\n    xDif = x - xPrev;\n    threshold = _config.autoKillThreshold;\n\n    if (data.x < 0) {\n      //can't scroll to a position less than 0! Might happen if someone uses a Back.easeOut or Elastic.easeOut when scrolling back to the top of the page (for example)\n      data.x = 0;\n    }\n\n    if (data.y < 0) {\n      data.y = 0;\n    }\n\n    if (autoKill) {\n      //note: iOS has a bug that throws off the scroll by several pixels, so we need to check if it's within 7 pixels of the previous one that we set instead of just looking for an exact match.\n      if (!data.skipX && (xDif > threshold || xDif < -threshold) && x < _max(target, \"x\")) {\n        data.skipX = 1; //if the user scrolls separately, we should stop tweening!\n      }\n\n      if (!data.skipY && (yDif > threshold || yDif < -threshold) && y < _max(target, \"y\")) {\n        data.skipY = 1; //if the user scrolls separately, we should stop tweening!\n      }\n\n      if (data.skipX && data.skipY) {\n        tween.kill();\n        data.vars.onAutoKill && data.vars.onAutoKill.apply(tween, data.vars.onAutoKillParams || []);\n      }\n    }\n\n    if (isWin) {\n      _window.scrollTo(!data.skipX ? data.x : x, !data.skipY ? data.y : y);\n    } else {\n      data.skipY || (target.scrollTop = data.y);\n      data.skipX || (target.scrollLeft = data.x);\n    }\n\n    if (snap && (ratio === 1 || ratio === 0)) {\n      y = target.scrollTop;\n      x = target.scrollLeft;\n      snapInline ? target.style.scrollSnapType = snapInline : target.style.removeProperty(\"scroll-snap-type\");\n      target.scrollTop = y + 1; // bug in Safari causes the element to totally reset its scroll position when scroll-snap-type changes, so we need to set it to a slightly different value and then back again to work around this bug.\n\n      target.scrollLeft = x + 1;\n      target.scrollTop = y;\n      target.scrollLeft = x;\n    }\n\n    data.xPrev = data.x;\n    data.yPrev = data.y;\n    ScrollTrigger && ScrollTrigger.update();\n  },\n  kill: function kill(property) {\n    var both = property === \"scrollTo\",\n        i = this._props.indexOf(property);\n\n    if (both || property === \"scrollTo_x\") {\n      this.skipX = 1;\n    }\n\n    if (both || property === \"scrollTo_y\") {\n      this.skipY = 1;\n    }\n\n    i > -1 && this._props.splice(i, 1);\n    return !this._props.length;\n  }\n};\nScrollToPlugin.max = _max;\nScrollToPlugin.getOffset = _getOffset;\nScrollToPlugin.buildGetter = _buildGetter;\n\nScrollToPlugin.config = function (vars) {\n  _config || _initCore() || (_config = gsap.config()); // in case the window hasn't been defined yet.\n\n  for (var p in vars) {\n    _config[p] = vars[p];\n  }\n};\n\n_getGSAP() && gsap.registerPlugin(ScrollToPlugin);\nexport { ScrollToPlugin as default };"], "mappings": ";AAUA,IAAI;AAAJ,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMI;AANJ,IAOI;AAPJ,IAQI,gBAAgB,SAASA,iBAAgB;AAC3C,SAAO,OAAO,WAAW;AAC3B;AAVA,IAWI,WAAW,SAASC,YAAW;AACjC,SAAO,QAAQ,cAAc,MAAM,OAAO,OAAO,SAAS,KAAK,kBAAkB;AACnF;AAbA,IAcI,YAAY,SAASC,WAAU,OAAO;AACxC,SAAO,OAAO,UAAU;AAC1B;AAhBA,IAiBI,cAAc,SAASC,aAAY,OAAO;AAC5C,SAAO,OAAO,UAAU;AAC1B;AAnBA,IAoBI,OAAO,SAASC,MAAK,SAAS,MAAM;AACtC,MAAI,MAAM,SAAS,MAAM,UAAU,UAC/B,SAAS,WAAW,KACpB,SAAS,WAAW;AACxB,SAAO,YAAY,WAAW,YAAY,UAAU,YAAY,QAAQ,KAAK,IAAI,OAAO,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,QAAQ,UAAU,GAAG,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM,IAAI,QAAQ,WAAW,GAAG;AAC1N;AAzBA,IA0BI,eAAe,SAASC,cAAa,GAAG,MAAM;AAEhD,MAAI,IAAI,YAAY,SAAS,MAAM,SAAS;AAE5C,MAAI,MAAM,SAAS;AACjB,QAAI,EAAE,eAAe,MAAM;AACzB,UAAI,SAAS,KAAK,YAAY,IAAI;AAAA,IACpC,OAAO;AACL,UAAI,OAAO,CAAC,KAAK,OAAO,SAAS;AAAA,IACnC;AAAA,EACF;AAEA,SAAO,WAAY;AACjB,WAAO,EAAE,CAAC;AAAA,EACZ;AACF;AAzCA,IA0CI,SAAS,SAASC,QAAO,OAAO,OAAO,QAAQ,SAAS;AAC1D,cAAY,KAAK,MAAM,QAAQ,MAAM,OAAO,QAAQ,OAAO;AAE3D,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,UAAU,KAAK,KAAK,UAAU,SAAS,MAAM,OAAO,CAAC,MAAM,MAAM;AAAA,MACtE,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI;AAAA,MACF,GAAG;AAAA,IACL;AAAA,EACF,WAAW,MAAM,UAAU;AACzB,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF,OAAO;AACL,QAAI,SAAS,CAAC,GACV;AAEJ,SAAK,KAAK,OAAO;AACf,aAAO,CAAC,IAAI,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,OAAO,QAAQ,OAAO,IAAI,MAAM,CAAC;AAAA,IACtG;AAEA,WAAO;AAAA,EACT;AACF;AAnEA,IAoEI,aAAa,SAASC,YAAW,SAAS,WAAW;AACvD,YAAU,SAAS,OAAO,EAAE,CAAC;AAE7B,MAAI,CAAC,WAAW,CAAC,QAAQ,uBAAuB;AAC9C,WAAO,QAAQ,KAAK,wCAAwC,KAAK;AAAA,MAC/D,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,sBAAsB,GACrC,SAAS,CAAC,aAAa,cAAc,WAAW,cAAc,OAC9D,QAAQ,SAAS;AAAA,IACnB,KAAK,OAAO,aAAa,QAAQ,eAAe,OAAO,aAAa,MAAM,aAAa;AAAA,IACvF,MAAM,OAAO,cAAc,QAAQ,eAAe,OAAO,cAAc,MAAM,cAAc;AAAA,EAC7F,IAAI,UAAU,sBAAsB,GAChC,UAAU;AAAA,IACZ,GAAG,KAAK,OAAO,MAAM;AAAA,IACrB,GAAG,KAAK,MAAM,MAAM;AAAA,EACtB;AAEA,MAAI,CAAC,UAAU,WAAW;AAExB,YAAQ,KAAK,aAAa,WAAW,GAAG,EAAE;AAC1C,YAAQ,KAAK,aAAa,WAAW,GAAG,EAAE;AAAA,EAC5C;AAEA,SAAO;AACT;AAhGA,IAiGI,YAAY,SAASC,WAAU,OAAO,QAAQ,MAAM,YAAY,QAAQ;AAC1E,SAAO,CAAC,MAAM,KAAK,KAAK,OAAO,UAAU,WAAW,WAAW,KAAK,IAAI,SAAS,UAAU,KAAK,KAAK,MAAM,OAAO,CAAC,MAAM,MAAM,WAAW,MAAM,OAAO,CAAC,CAAC,KAAK,MAAM,OAAO,CAAC,MAAM,MAAM,KAAK,KAAK,aAAa,SAAS,UAAU,QAAQ,KAAK,QAAQ,IAAI,IAAI,SAAS,KAAK,IAAI,KAAK,QAAQ,IAAI,GAAG,WAAW,OAAO,MAAM,EAAE,IAAI,IAAI,MAAM;AAC/U;AAnGA,IAoGI,YAAY,SAASC,aAAY;AACnC,SAAO,SAAS;AAEhB,MAAI,cAAc,KAAK,QAAQ,OAAO,aAAa,eAAe,SAAS,MAAM;AAC/E,cAAU;AACV,YAAQ,SAAS;AACjB,aAAS,SAAS;AAClB,eAAW,KAAK,MAAM;AACtB,SAAK,OAAO;AAAA,MACV,mBAAmB;AAAA,IACrB,CAAC;AACD,cAAU,KAAK,OAAO;AACtB,mBAAe;AAAA,EACjB;AACF;AAEO,IAAI,iBAAiB;AAAA,EAC1B,SAAS;AAAA,EACT,MAAM;AAAA,EACN,SAAS;AAAA,EACT,UAAU,SAAS,SAAS,MAAM;AAChC,WAAO;AAEP,cAAU;AAAA,EACZ;AAAA,EACA,MAAM,SAAS,KAAK,QAAQ,OAAO,OAAO,OAAO,SAAS;AACxD,oBAAgB,UAAU;AAC1B,QAAI,OAAO,MACP,WAAW,KAAK,YAAY,QAAQ,gBAAgB;AACxD,SAAK,QAAQ,WAAW;AACxB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,YAAQ,OAAO,OAAO,OAAO,QAAQ,OAAO;AAC5C,SAAK,OAAO;AACZ,SAAK,WAAW,CAAC,EAAE,cAAc,QAAQ,QAAQ,SAAS;AAC1D,SAAK,OAAO,aAAa,QAAQ,GAAG;AACpC,SAAK,OAAO,aAAa,QAAQ,GAAG;AACpC,SAAK,IAAI,KAAK,QAAQ,KAAK,KAAK;AAChC,SAAK,IAAI,KAAK,QAAQ,KAAK,KAAK;AAChC,sBAAkB,gBAAgB,KAAK,KAAK,QAAQ,EAAE;AACtD,SAAK,YAAY,QAAQ,gBAAgB,MAAM,YAAY,KAAK,IAAI,QAAQ;AAAA,MAC1E,gBAAgB;AAAA,IAClB,CAAC;AAED,QAAI,YAAY,aAAa,QAAQ;AAEnC,WAAK,OAAO;AACZ,WAAK,aAAa,OAAO,MAAM;AAC/B,aAAO,MAAM,iBAAiB;AAAA,IAChC;AAEA,QAAI,MAAM,KAAK,MAAM;AACnB,WAAK,IAAI,MAAM,KAAK,KAAK,GAAG,UAAU,MAAM,GAAG,QAAQ,KAAK,KAAK,GAAG,MAAM,WAAW,CAAC,GAAG,OAAO,OAAO;AAEvG,WAAK,OAAO,KAAK,YAAY;AAAA,IAC/B,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAEA,QAAI,MAAM,KAAK,MAAM;AACnB,WAAK,IAAI,MAAM,KAAK,KAAK,GAAG,UAAU,MAAM,GAAG,QAAQ,KAAK,KAAK,GAAG,MAAM,WAAW,CAAC,GAAG,OAAO,OAAO;AAEvG,WAAK,OAAO,KAAK,YAAY;AAAA,IAC/B,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,OAAO,OAAO,MAAM;AACnC,QAAI,KAAK,KAAK,KACV,SAAS,KAAK,QACd,QAAQ,KAAK,OACb,WAAW,KAAK,UAChB,QAAQ,KAAK,OACb,QAAQ,KAAK,OACb,QAAQ,KAAK,OACb,OAAO,KAAK,MACZ,aAAa,KAAK,YAClB,GACA,GACA,MACA,MACA;AAEJ,WAAO,IAAI;AACT,SAAG,EAAE,OAAO,GAAG,CAAC;AAChB,WAAK,GAAG;AAAA,IACV;AAEA,QAAI,SAAS,CAAC,KAAK,QAAQ,KAAK,KAAK,IAAI;AACzC,QAAI,SAAS,CAAC,KAAK,QAAQ,KAAK,KAAK,IAAI;AACzC,WAAO,IAAI;AACX,WAAO,IAAI;AACX,gBAAY,QAAQ;AAEpB,QAAI,KAAK,IAAI,GAAG;AAEd,WAAK,IAAI;AAAA,IACX;AAEA,QAAI,KAAK,IAAI,GAAG;AACd,WAAK,IAAI;AAAA,IACX;AAEA,QAAI,UAAU;AAEZ,UAAI,CAAC,KAAK,UAAU,OAAO,aAAa,OAAO,CAAC,cAAc,IAAI,KAAK,QAAQ,GAAG,GAAG;AACnF,aAAK,QAAQ;AAAA,MACf;AAEA,UAAI,CAAC,KAAK,UAAU,OAAO,aAAa,OAAO,CAAC,cAAc,IAAI,KAAK,QAAQ,GAAG,GAAG;AACnF,aAAK,QAAQ;AAAA,MACf;AAEA,UAAI,KAAK,SAAS,KAAK,OAAO;AAC5B,cAAM,KAAK;AACX,aAAK,KAAK,cAAc,KAAK,KAAK,WAAW,MAAM,OAAO,KAAK,KAAK,oBAAoB,CAAC,CAAC;AAAA,MAC5F;AAAA,IACF;AAEA,QAAI,OAAO;AACT,cAAQ,SAAS,CAAC,KAAK,QAAQ,KAAK,IAAI,GAAG,CAAC,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,IACrE,OAAO;AACL,WAAK,UAAU,OAAO,YAAY,KAAK;AACvC,WAAK,UAAU,OAAO,aAAa,KAAK;AAAA,IAC1C;AAEA,QAAI,SAAS,UAAU,KAAK,UAAU,IAAI;AACxC,UAAI,OAAO;AACX,UAAI,OAAO;AACX,mBAAa,OAAO,MAAM,iBAAiB,aAAa,OAAO,MAAM,eAAe,kBAAkB;AACtG,aAAO,YAAY,IAAI;AAEvB,aAAO,aAAa,IAAI;AACxB,aAAO,YAAY;AACnB,aAAO,aAAa;AAAA,IACtB;AAEA,SAAK,QAAQ,KAAK;AAClB,SAAK,QAAQ,KAAK;AAClB,qBAAiB,cAAc,OAAO;AAAA,EACxC;AAAA,EACA,MAAM,SAAS,KAAK,UAAU;AAC5B,QAAI,OAAO,aAAa,YACpB,IAAI,KAAK,OAAO,QAAQ,QAAQ;AAEpC,QAAI,QAAQ,aAAa,cAAc;AACrC,WAAK,QAAQ;AAAA,IACf;AAEA,QAAI,QAAQ,aAAa,cAAc;AACrC,WAAK,QAAQ;AAAA,IACf;AAEA,QAAI,MAAM,KAAK,OAAO,OAAO,GAAG,CAAC;AACjC,WAAO,CAAC,KAAK,OAAO;AAAA,EACtB;AACF;AACA,eAAe,MAAM;AACrB,eAAe,YAAY;AAC3B,eAAe,cAAc;AAE7B,eAAe,SAAS,SAAU,MAAM;AACtC,aAAW,UAAU,MAAM,UAAU,KAAK,OAAO;AAEjD,WAAS,KAAK,MAAM;AAClB,YAAQ,CAAC,IAAI,KAAK,CAAC;AAAA,EACrB;AACF;AAEA,SAAS,KAAK,KAAK,eAAe,cAAc;", "names": ["_windowExists", "_getGSAP", "_isString", "_isFunction", "_max", "_buildGetter", "_clean", "_getOffset", "_parseVal", "_initCore"]}