@import url("https://fonts.googleapis.com/css2?family=DM+Mono:ital,wght@0,300;0,400;0,500;1,300;1,400;1,500&family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap");

:root {
    --dark: #000;
    --light: #f9f4eb;
    --light2: #f0ece5;
    --accent-1: #ceb2f4;
    --accent-2: #faade6;
    --accent-3: #f7c877;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "DM Sans";
}

h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

p {
    font-size: 1.1rem;
    font-weight: 500;
}

span {
    text-transform: uppercase;
    font-family: "DM Mono";
    font-size: 0.75rem;
    font-weight: 500;
}

nav {
    position: fixed;
    width: 100vw;
    padding: 1.875rem 3.125rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 2;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.625rem;
}

.logo-img {
    width: 1.875rem;
    height: 1.875rem;
    object-fit: contain;
}

.logo span,
.menu-btn span {
    font-family: "DM Sans";
    font-size: 1rem;
    font-weight: 500;
    padding: 0.9375rem 1.375rem;
    border-radius: 0.3125rem;
}

.logo span {
    display: flex;
    align-items: center;
    gap: 0.3125rem;
    padding: 0.75rem 1.375rem;
    background-color: var(--dark);
    color: var(--light);
    border-radius: 1.875rem;
}

.menu-btn span {
    background-color: var(--light2);
    color: var(--dark);
    border-radius: 1.875rem;
}

.menu-btn span:hover {
    background-color: var(--dark);
    color: var(--light);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

section {
    position: relative;
    width: 100vw;
    height: 100svh;
    padding: 2rem;
    overflow: hidden;
}

.hero {
    background-color: var(--light);
    color: var(--dark);
}

.about {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--dark);
    color: var(--light);
    padding: 4rem 2rem;
}

.about-content {
    max-width: 1000px;
    text-align: center;
}

.about-content h1 {
    font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    font-size: 3.6rem;
    font-weight: 400;
    font-style: italic;
    line-height: 1.3;
    margin-bottom: 3rem;
    color: var(--light);
}

.about-tagline {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: nowrap;
}

.about-tagline span {
    font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    font-size: 1.4rem;
    font-weight: 400;
    font-style: italic;
    text-transform: none;
    letter-spacing: 0.05em;
    color: var(--light);
    background: rgba(0, 0, 0, 0.08);
    padding: 1rem 2rem;
    border-radius: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.238);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: -1rem;
}

.about-tagline span:hover {
    transform: none;
    box-shadow: none;
}

.about-tagline span:nth-child(2) {
    background: none;
}

.about-tagline span:nth-child(3) {
    background: none;
}

.outro {
    background-color: var(--dark);
    color: var(--light);
    padding: 4rem 2rem;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.outro-content {
    max-width: 1200px;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 4rem;
}

.calendly-section {
    flex: 1;
    max-width: 500px;
}

.calendly-section .calendly-inline-widget {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.outro-text {
    flex: 1;
    padding-left: 2rem;
}

.outro-text h1 {
    font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    font-size: 3.5rem;
    font-weight: 400;
    font-style: italic;
    line-height: 1.2;
    color: var(--light);
}

/* Footer styles */
.footer {
    background-color: var(--dark);
    color: var(--light);
    padding: 4rem 2rem;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 800px;
    margin: 0 auto;
}

.footer-content h2 {
    font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    font-size: 2.5rem;
    font-weight: 400;
    font-style: italic;
    margin-bottom: 2rem;
    color: var(--light);
}

.hero-cards {
    position: absolute;
    top: 55%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.hero-cards .card {
    flex: 1;
    position: relative;
    aspect-ratio: 5 / 7;
    padding: 0.75rem;
    border-radius: 1.0rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: transform 0.3s ease;
}

.card-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.hero-cards .card span {
    font-size: 0.7rem;
}

.hero-cards .card#hero-card-1 {
    background-color: var(--accent-1);
    transform-origin: top right;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-cards .card#hero-card-1 .altara-text {
    font-size: 2rem;
    text-align: center;
    padding: 1rem;
}

.hero-cards .card#hero-card-2 {
    background-color: var(--accent-2);
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-cards .card#hero-card-2 .altara-text {
    font-size: 2rem;
    text-align: center;
    padding: 1rem;
}

.hero-cards .card#hero-card-3 {
    background-color: var(--accent-3);
    transform-origin: top left;
    z-index: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-cards .card#hero-card-3 .altara-text {
    font-size: 2rem;
    text-align: center;
    padding: 1rem;
}

.services {
    padding: 8rem 2rem;
}

.hero-title{
    position: absolute;
    width: 100%;
    text-align: center;
    top: 22%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    will-change: transform;
}

.hero-title h1 {
    font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    font-size: 4rem;
    font-weight: 400;
    font-style: italic;
    font-variant: normal;
    font-kerning: auto;
    font-optical-sizing: auto;
    font-stretch: 100%;
    font-variation-settings: normal;
    font-feature-settings: normal;
    text-transform: none;
    text-decoration: none solid rgb(255, 255, 255);
    text-align: center;
    text-indent: 0px;
    color: var(--dark);
    margin: 0;
}

.services-header {
    position: relative;
    width: 100%;
    text-align: center;
    transform: translateY(200%);
    will-change: transform;
}

.services-header h1 {
    font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    font-size: 3.6rem;
    font-weight: 400;
    font-style: italic;
    color: var(--dark);
}

.cards {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100svh;
    display: flex;
    justify-content: center;
    z-index: -1;
    background-color: var(--light);
}

.cards-container {
    position: relative;
    width: 75%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4rem;
    transform: translateY(5%);
} 

.cards-container .card {
    flex: 1;
    position: relative;
    aspect-ratio: 5/7;
    perspective: 1000px;
}

.cards-container .card .card-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    animation: floating 2s infinite ease-in-out;
}

@keyframes floating {
    0% {
        transform: translate(-50%, -50%);
    }
    
    50% {
        transform: translate(-50%, -55%);
    }
    
    100% {
        transform: translate(-50%, -50%);
    }
}

#card-1 .card-wrapper {
    animation-delay: 0;
}

#card-2 .card-wrapper {
    animation-delay: 0.25s;
}

#card-3 .card-wrapper {
    animation-delay: 0.5s;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
}

.flip-card-front,
.flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 1.5rem;
    backface-visibility: hidden;
    overflow: hidden;
}

.flip-card-front {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.flip-card-front .altara-text {
    font-size: 2rem;
    text-align: center;
    padding: 1rem;
}

#card-1 .flip-card-front {
    background-color: var(--accent-1);
}

#card-2 .flip-card-front {
    background-color: var(--accent-2);
}

#card-3 .flip-card-front {
    background-color: var(--accent-3);
}

.flip-card-back {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 2rem;
    background-color: #fff;
    transform: rotateY(180deg);
}

.card-copy {
    width: 100%;  
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.card-copy p {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1rem;
    background-color: var(--light2);
    border-radius: 0.25rem;
}

#card-1 {
    transform: translateX(100%) translateY(-100%) rotate(-5deg) scale(0.25);
    z-index: 2;
}

#card-2 {
    transform: translateX(0%) translateY(-100%) rotate(0deg) scale(0.25);
    z-index: 1;
}

#card-3 {
    transform: translateX(-100%) translateY(-100%) rotate(5deg) scale(0.25);
    z-index: 0;
}

.cards-container .card {
    opacity: 0;
}

.flip-card-back .altara-text {
    font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    font-size: 2rem;
    font-weight: 400;
    font-style: italic;
    text-align: center;
    color: var(--dark);
    padding: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* Responsive design */
@media (max-width: 768px) {
    .outro-content {
        flex-direction: column;
        gap: 2rem;
    }
    
    .outro-text {
        padding-left: 0;
        text-align: center;
    }
    
    .outro-text h1 {
        font-size: 2.5rem;
    }
    
    .calendly-section {
        max-width: 100%;
    }
}

